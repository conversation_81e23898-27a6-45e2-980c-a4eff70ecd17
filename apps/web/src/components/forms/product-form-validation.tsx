'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { AlertTriangle, CheckCircle, Info, Loader2 } from 'lucide-react';
import { ValidatedInput, ValidatedTextarea, ValidatedSelect, ValidatedCheckbox } from './validated-input';
import { useProductFormValidation } from '@/hooks/use-form-validation';

interface ProductFormValidationProps {
  initialData?: any;
  onSave: (data: any) => void;
  onCancel: () => void;
  isLoading?: boolean;
  className?: string;
}

export function ProductFormValidation({
  initialData,
  onSave,
  onCancel,
  isLoading = false,
  className
}: ProductFormValidationProps) {
  const {
    values,
    errors,
    isValid,
    setValue,
    validateAll,
    getFieldError,
    hasFieldError
  } = useProductFormValidation(initialData || {});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const isFormValid = await validateAll();
    if (isFormValid) {
      onSave(values);
    }
  };

  const getValidationStatus = (field: string) => ({
    isInvalid: hasFieldError(field),
    message: getFieldError(field),
  });

  // Currency options
  const currencyOptions = [
    { value: 'USD', label: 'USD - US Dollar' },
    { value: 'EUR', label: 'EUR - Euro' },
    { value: 'GBP', label: 'GBP - British Pound' },
    { value: 'CNY', label: 'CNY - Chinese Yuan' },
    { value: 'JPY', label: 'JPY - Japanese Yen' },
    { value: 'KRW', label: 'KRW - Korean Won' },
    { value: 'CAD', label: 'CAD - Canadian Dollar' },
    { value: 'AUD', label: 'AUD - Australian Dollar' },
  ];

  // Weight unit options
  const weightUnitOptions = [
    { value: 'kg', label: 'Kilograms (kg)' },
    { value: 'lb', label: 'Pounds (lb)' },
    { value: 'g', label: 'Grams (g)' },
    { value: 'oz', label: 'Ounces (oz)' },
  ];

  // Status options
  const statusOptions = [
    { value: 'DRAFT', label: 'Draft' },
    { value: 'ACTIVE', label: 'Active' },
    { value: 'INACTIVE', label: 'Inactive' },
    { value: 'DISCONTINUED', label: 'Discontinued' },
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Product Information</CardTitle>
        <div className="flex items-center gap-2">
          {isValid ? (
            <Badge variant="outline" className="text-green-600 border-green-600">
              <CheckCircle className="w-3 h-3 mr-1" />
              Valid
            </Badge>
          ) : (
            <Badge variant="outline" className="text-orange-600 border-orange-600">
              <AlertTriangle className="w-3 h-3 mr-1" />
              {Object.keys(errors).length} Error{Object.keys(errors).length !== 1 ? 's' : ''}
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="specifications">Specifications</TabsTrigger>
              <TabsTrigger value="compliance">Compliance</TabsTrigger>
              <TabsTrigger value="marketing">Marketing</TabsTrigger>
            </TabsList>

            {/* Basic Information Tab */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ValidatedInput
                  label="Product Name"
                  required
                  value={values.name || ''}
                  onChange={(value) => setValue('name', value)}
                  validation={getValidationStatus('name')}
                  placeholder="Enter product name"
                />
                
                <ValidatedInput
                  label="SKU"
                  required
                  value={values.sku || ''}
                  onChange={(value) => setValue('sku', value)}
                  validation={getValidationStatus('sku')}
                  placeholder="Enter SKU"
                  description="Stock Keeping Unit - unique identifier"
                />
                
                <ValidatedSelect
                  label="Status"
                  required
                  value={values.status || 'DRAFT'}
                  onChange={(value) => setValue('status', value)}
                  validation={getValidationStatus('status')}
                  options={statusOptions}
                />
                
                <ValidatedInput
                  label="Category"
                  value={values.category || ''}
                  onChange={(value) => setValue('category', value)}
                  validation={getValidationStatus('category')}
                  placeholder="Enter product category"
                />
              </div>

              <ValidatedTextarea
                label="Description"
                value={values.description || ''}
                onChange={(value) => setValue('description', value)}
                validation={getValidationStatus('description')}
                placeholder="Enter product description"
                rows={4}
                maxLength={1000}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <ValidatedInput
                  label="Price"
                  type="number"
                  step={0.01}
                  min={0}
                  value={values.price || 0}
                  onChange={(value) => setValue('price', Number(value))}
                  validation={getValidationStatus('price')}
                  placeholder="0.00"
                />
                
                <ValidatedSelect
                  label="Currency"
                  value={values.currency || 'USD'}
                  onChange={(value) => setValue('currency', value)}
                  validation={getValidationStatus('currency')}
                  options={currencyOptions}
                />
                
                <ValidatedInput
                  label="MOQ"
                  type="number"
                  min={1}
                  value={values.moq || 1}
                  onChange={(value) => setValue('moq', Number(value))}
                  validation={getValidationStatus('moq')}
                  placeholder="1"
                  description="Minimum Order Quantity"
                />
              </div>
            </TabsContent>

            {/* Specifications Tab */}
            <TabsContent value="specifications" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ValidatedInput
                  label="Manufacturing Time (days)"
                  type="number"
                  min={0}
                  value={values.manufacturingTime || 0}
                  onChange={(value) => setValue('manufacturingTime', Number(value))}
                  validation={getValidationStatus('manufacturingTime')}
                  placeholder="0"
                />
                
                <ValidatedInput
                  label="Production Capacity"
                  type="number"
                  min={0}
                  value={values.productionCapacity || 0}
                  onChange={(value) => setValue('productionCapacity', Number(value))}
                  validation={getValidationStatus('productionCapacity')}
                  placeholder="0"
                />
                
                <ValidatedInput
                  label="Lead Time (days)"
                  type="number"
                  min={0}
                  value={values.leadTime || 0}
                  onChange={(value) => setValue('leadTime', Number(value))}
                  validation={getValidationStatus('leadTime')}
                  placeholder="0"
                />
                
                <ValidatedInput
                  label="Weight"
                  type="number"
                  step={0.01}
                  min={0}
                  value={values.weight || 0}
                  onChange={(value) => setValue('weight', Number(value))}
                  validation={getValidationStatus('weight')}
                  placeholder="0.00"
                />
                
                <ValidatedSelect
                  label="Weight Unit"
                  value={values.weightUnit || 'kg'}
                  onChange={(value) => setValue('weightUnit', value)}
                  validation={getValidationStatus('weightUnit')}
                  options={weightUnitOptions}
                />
              </div>

              <ValidatedTextarea
                label="Material Composition"
                value={values.materialComposition || ''}
                onChange={(value) => setValue('materialComposition', value)}
                validation={getValidationStatus('materialComposition')}
                placeholder="Describe the materials used"
                rows={3}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ValidatedInput
                  label="Technical Data Sheet URL"
                  type="url"
                  value={values.technicalDataSheet || ''}
                  onChange={(value) => setValue('technicalDataSheet', value)}
                  validation={getValidationStatus('technicalDataSheet')}
                  placeholder="https://..."
                />
                
                <ValidatedInput
                  label="User Manual URL"
                  type="url"
                  value={values.userManual || ''}
                  onChange={(value) => setValue('userManual', value)}
                  validation={getValidationStatus('userManual')}
                  placeholder="https://..."
                />
              </div>
            </TabsContent>

            {/* Compliance Tab */}
            <TabsContent value="compliance" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ValidatedCheckbox
                  label="CE Certified"
                  checked={values.ceCertified || false}
                  onChange={(checked) => setValue('ceCertified', checked)}
                  validation={getValidationStatus('ceCertified')}
                  description="European Conformity certification"
                />
                
                <ValidatedCheckbox
                  label="FCC Certified"
                  checked={values.fccCertified || false}
                  onChange={(checked) => setValue('fccCertified', checked)}
                  validation={getValidationStatus('fccCertified')}
                  description="Federal Communications Commission certification"
                />
                
                <ValidatedCheckbox
                  label="RoHS Compliant"
                  checked={values.rohsCompliant || false}
                  onChange={(checked) => setValue('rohsCompliant', checked)}
                  validation={getValidationStatus('rohsCompliant')}
                  description="Restriction of Hazardous Substances compliance"
                />
                
                <ValidatedCheckbox
                  label="ISO Certified"
                  checked={values.isoCertified || false}
                  onChange={(checked) => setValue('isoCertified', checked)}
                  validation={getValidationStatus('isoCertified')}
                  description="International Organization for Standardization certification"
                />
              </div>

              <ValidatedTextarea
                label="Compliance Notes"
                value={values.complianceNotes || ''}
                onChange={(value) => setValue('complianceNotes', value)}
                validation={getValidationStatus('complianceNotes')}
                placeholder="Additional compliance information"
                rows={3}
              />
            </TabsContent>

            {/* Marketing Tab */}
            <TabsContent value="marketing" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ValidatedInput
                  label="Brand"
                  value={values.brand || ''}
                  onChange={(value) => setValue('brand', value)}
                  validation={getValidationStatus('brand')}
                  placeholder="Enter brand name"
                />
                
                <ValidatedInput
                  label="Model"
                  value={values.model || ''}
                  onChange={(value) => setValue('model', value)}
                  validation={getValidationStatus('model')}
                  placeholder="Enter model number"
                />
              </div>

              <ValidatedTextarea
                label="Marketing Description"
                value={values.marketingDescription || ''}
                onChange={(value) => setValue('marketingDescription', value)}
                validation={getValidationStatus('marketingDescription')}
                placeholder="Marketing-focused product description"
                rows={4}
                maxLength={2000}
              />

              <ValidatedTextarea
                label="Key Features"
                value={values.keyFeatures || ''}
                onChange={(value) => setValue('keyFeatures', value)}
                validation={getValidationStatus('keyFeatures')}
                placeholder="List key product features (one per line)"
                rows={4}
              />
            </TabsContent>
          </Tabs>

          <Separator />

          {/* Form Actions */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              {isLoading && <Loader2 className="w-4 h-4 animate-spin" />}
              <span className="text-sm text-muted-foreground">
                {isValid ? 'Form is valid' : `${Object.keys(errors).length} validation error(s)`}
              </span>
            </div>
            
            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isLoading || !isValid}
                className={!isValid ? 'opacity-50' : ''}
              >
                {isLoading ? 'Saving...' : 'Save Product'}
              </Button>
            </div>
          </div>

          {/* Validation Summary */}
          {Object.keys(errors).length > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Please fix the following validation errors:
                <ul className="list-disc list-inside mt-2 space-y-1">
                  {Object.entries(errors).map(([field, message]) => (
                    <li key={field} className="text-sm">
                      <strong>{field}:</strong> {message}
                    </li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
