"use client"

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { CheckCircle, Circle, ArrowLeft, ArrowRight } from 'lucide-react';
// Temporarily use direct API integration instead of Payload CMS
// import {
//   mapOnboardingToPayloadFactory,
//   createFactoryInPayload,
//   markOnboardingComplete,
//   type FactoryOnboardingData
// } from '../../utils/payload-integration';

// Define the onboarding data type locally
export interface FactoryOnboardingData {
  basicInfo: any;
  businessDetails: any;
  factoryConfig: any;
  adminSetup: any;
  verification: any;
}
import { useAuth } from '../../contexts/auth-context';

// Step schemas
const basicInfoSchema = z.object({
  factoryName: z.string().min(2, 'Factory name must be at least 2 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  industry: z.string().min(1, 'Please select an industry'),
  contactEmail: z.string().email('Please enter a valid email'),
  contactPhone: z.string().min(10, 'Please enter a valid phone number'),
  address: z.string().min(5, 'Please enter a complete address'),
  city: z.string().min(2, 'Please enter a city'),
  country: z.string().min(2, 'Please select a country'),
  timezone: z.string().min(1, 'Please select a timezone'),
});

const businessDetailsSchema = z.object({
  businessType: z.string().min(1, 'Please select a business type'),
  businessLicense: z.string().optional(),
  taxId: z.string().min(5, 'Please enter a valid tax ID'),
  establishedYear: z.number().min(1900).max(new Date().getFullYear()),
  employeeCount: z.string().min(1, 'Please select employee count range'),
  annualRevenue: z.string().optional(),
});

const factoryConfigSchema = z.object({
  productCategories: z.array(z.string()).min(1, 'Please select at least one product category'),
  productionCapacity: z.string().min(1, 'Please enter production capacity'),
  shippingMethods: z.array(z.string()).min(1, 'Please select at least one shipping method'),
  paymentMethods: z.array(z.string()).min(1, 'Please select at least one payment method'),
  certifications: z.array(z.string()).optional(),
  minimumOrderQuantity: z.string().min(1, 'Please enter minimum order quantity'),
});

const adminSetupSchema = z.object({
  adminName: z.string().min(2, 'Admin name must be at least 2 characters'),
  adminEmail: z.string().email('Please enter a valid email'),
  adminPhone: z.string().min(10, 'Please enter a valid phone number'),
  adminRole: z.string().min(1, 'Please select an admin role'),
  teamMembers: z.array(z.object({
    name: z.string().min(2, 'Name must be at least 2 characters'),
    email: z.string().email('Please enter a valid email'),
    role: z.string().min(1, 'Please select a role'),
  })).optional(),
});

const verificationSchema = z.object({
  termsAccepted: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
  privacyAccepted: z.boolean().refine(val => val === true, 'You must accept the privacy policy'),
  documentsUploaded: z.boolean().refine(val => val === true, 'Please upload all required documents'),
});

// Combined schema for the entire wizard
const factoryOnboardingSchema = z.object({
  basicInfo: basicInfoSchema,
  businessDetails: businessDetailsSchema,
  factoryConfig: factoryConfigSchema,
  adminSetup: adminSetupSchema,
  verification: verificationSchema,
});

// Remove duplicate type definition - using interface above

interface WizardStep {
  id: number;
  title: string;
  description: string;
  component: React.ComponentType<any>;
  schema: z.ZodSchema<any>;
}

// Progress indicator component
interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  steps: WizardStep[];
}

function ProgressIndicator({ currentStep, totalSteps, steps }: ProgressIndicatorProps) {
  const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold text-gray-900">Factory Registration</h2>
        <Badge variant="outline" className="text-sm">
          Step {currentStep} of {totalSteps}
        </Badge>
      </div>
      
      <Progress value={progress} className="mb-6" />
      
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const stepNumber = index + 1;
          const isCompleted = stepNumber < currentStep;
          const isCurrent = stepNumber === currentStep;
          
          return (
            <div key={step.id} className="flex flex-col items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 mb-2 ${
                isCompleted 
                  ? 'bg-blue-600 border-blue-600 text-white' 
                  : isCurrent 
                    ? 'border-blue-600 text-blue-600 bg-white' 
                    : 'border-gray-300 text-gray-400 bg-white'
              }`}>
                {isCompleted ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <span className="text-sm font-medium">{stepNumber}</span>
                )}
              </div>
              <span className={`text-xs text-center max-w-20 ${
                isCurrent ? 'text-blue-600 font-medium' : 'text-gray-500'
              }`}>
                {step.title}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Main wizard component
export function FactoryOnboardingWizard() {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<FactoryOnboardingData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Import step components (will be created separately)
  const BasicInfoStep = React.lazy(() => import('./steps/basic-info-step'));
  const BusinessDetailsStep = React.lazy(() => import('./steps/business-details-step'));
  const FactoryConfigStep = React.lazy(() => import('./steps/factory-config-step'));
  const AdminSetupStep = React.lazy(() => import('./steps/admin-setup-step'));
  const VerificationStep = React.lazy(() => import('./steps/verification-step'));

  const steps: WizardStep[] = [
    {
      id: 1,
      title: 'Basic Info',
      description: 'Factory name, description, and contact information',
      component: BasicInfoStep,
      schema: basicInfoSchema,
    },
    {
      id: 2,
      title: 'Business Details',
      description: 'Business type, license, and establishment information',
      component: BusinessDetailsStep,
      schema: businessDetailsSchema,
    },
    {
      id: 3,
      title: 'Configuration',
      description: 'Product categories, capabilities, and shipping options',
      component: FactoryConfigStep,
      schema: factoryConfigSchema,
    },
    {
      id: 4,
      title: 'Admin Setup',
      description: 'Admin account and team member management',
      component: AdminSetupStep,
      schema: adminSetupSchema,
    },
    {
      id: 5,
      title: 'Verification',
      description: 'Document verification and terms acceptance',
      component: VerificationStep,
      schema: verificationSchema,
    },
  ];

  const currentStepData = steps[currentStep - 1];
  const CurrentStepComponent = currentStepData.component;

  const handleNext = async (stepData: any) => {
    console.log('handleNext called with:', { currentStep, stepsLength: steps.length, stepData });

    // Update form data with current step data
    const stepKey = getStepKey(currentStep);
    const updatedFormData = {
      ...formData,
      [stepKey]: stepData,
    };

    setFormData(updatedFormData);

    if (currentStep < steps.length) {
      console.log('Moving to next step:', currentStep + 1);
      setCurrentStep(currentStep + 1);
    } else {
      console.log('Final submission triggered');
      // Final submission with updated data
      await handleFinalSubmission(updatedFormData);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getStepKey = (step: number): keyof FactoryOnboardingData => {
    const keys: (keyof FactoryOnboardingData)[] = [
      'basicInfo',
      'businessDetails', 
      'factoryConfig',
      'adminSetup',
      'verification'
    ];
    return keys[step - 1];
  };

  const handleFinalSubmission = async (finalFormData?: Partial<FactoryOnboardingData>) => {
    setIsSubmitting(true);
    try {
      const dataToSubmit = finalFormData || formData;
      console.log('Final submission:', dataToSubmit);

      // Validate that we have all required data and user authentication
      console.log('🔍 Debug - User object:', user);
      console.log('🔍 Debug - User auth0Id:', user?.auth0Id);
      console.log('🔍 Debug - User keys:', user ? Object.keys(user) : 'No user');
      console.log('🔍 Debug - Full user data:', JSON.stringify(user, null, 2));

      // Check for user ID in different possible properties
      const userId = user?.auth0Id || user?.id || user?.email;

      if (!userId) {
        console.error('❌ Authentication failed - no user ID found:', { user });
        throw new Error('User authentication required');
      }

      console.log('✅ User ID found:', userId);

      // Debug form data completeness
      console.log('🔍 Form data keys:', Object.keys(dataToSubmit));
      console.log('🔍 Form data structure:', {
        hasBasicInfo: !!dataToSubmit.basicInfo,
        hasBusinessDetails: !!dataToSubmit.businessDetails,
        hasFactoryConfig: !!dataToSubmit.factoryConfig,
        hasAdminSetup: !!dataToSubmit.adminSetup,
        hasVerification: !!dataToSubmit.verification,
      });
      console.log('🔍 Verification data:', dataToSubmit.verification);

      if (dataToSubmit.basicInfo && dataToSubmit.businessDetails && dataToSubmit.factoryConfig &&
          dataToSubmit.adminSetup && dataToSubmit.verification) {

        const completeOnboardingData = dataToSubmit as FactoryOnboardingData;

        // Create factory data object for API submission
        const factoryData = {
          // Basic Info
          name: completeOnboardingData.basicInfo.factoryName,
          description: completeOnboardingData.basicInfo.description,
          industry: completeOnboardingData.basicInfo.industry,
          email: completeOnboardingData.basicInfo.contactEmail,
          phone: completeOnboardingData.basicInfo.contactPhone,
          address: {
            street: completeOnboardingData.basicInfo.address,
            city: completeOnboardingData.basicInfo.city,
            country: completeOnboardingData.basicInfo.country,
            timezone: completeOnboardingData.basicInfo.timezone,
          },

          // Business Details
          businessType: completeOnboardingData.businessDetails.businessType,
          businessLicense: completeOnboardingData.businessDetails.businessLicense,
          taxId: completeOnboardingData.businessDetails.taxId,
          establishedYear: completeOnboardingData.businessDetails.establishedYear,
          employeeCount: completeOnboardingData.businessDetails.employeeCount,

          // Factory Configuration
          productCategories: completeOnboardingData.factoryConfig.productCategories,
          productionCapacity: completeOnboardingData.factoryConfig.productionCapacity,
          shippingMethods: completeOnboardingData.factoryConfig.shippingMethods,
          paymentMethods: completeOnboardingData.factoryConfig.paymentMethods,

          // Admin Setup
          adminName: completeOnboardingData.adminSetup.adminName,
          adminEmail: completeOnboardingData.adminSetup.adminEmail,
          adminPhone: completeOnboardingData.adminSetup.adminPhone,
          adminRole: completeOnboardingData.adminSetup.adminRole,

          // User Association
          userId: userId,
          status: 'PENDING',
        };

        // Submit to API endpoint
        const response = await fetch('/api/factories', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(factoryData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to create factory');
        }

        const createdFactory = await response.json();
        console.log('✅ Factory created successfully:', createdFactory);

        // Store completion status with correct keys
        localStorage.setItem('fc-china-onboarding-complete', 'true');
        localStorage.setItem('fc-china-factory-id', createdFactory.id);

        // Redirect to dashboard with success message
        window.location.href = `/dashboard?onboarding=complete&factory=${createdFactory.id}`;
      } else {
        throw new Error('Incomplete onboarding data');
      }
    } catch (error) {
      console.error('❌ Submission error:', error);
      // TODO: Show user-friendly error message
      alert(`Error creating factory: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <ProgressIndicator 
          currentStep={currentStep} 
          totalSteps={steps.length} 
          steps={steps}
        />

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="text-xl font-semibold">
              {currentStepData.title}
            </CardTitle>
            <CardDescription>
              {currentStepData.description}
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <React.Suspense fallback={<div>Loading...</div>}>
              <CurrentStepComponent
                onNext={handleNext}
                onPrevious={handlePrevious}
                initialData={formData[getStepKey(currentStep)]}
                canGoBack={currentStep > 1}
                isLastStep={currentStep === steps.length}
                isSubmitting={isSubmitting}
              />
            </React.Suspense>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
