'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Lock, 
  Plus, 
  Clock, 
  ShoppingCart, 
  FileText, 
  User, 
  ArrowRightLeft, 
  AlertCircle,
  Loader2,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { trpc } from '@/lib/trpc';

interface InventoryReservationManagerProps {
  productId: string;
  factoryId: string;
  inventoryLocations: any[];
}

interface InventoryReservation {
  id: string;
  reservationType: 'ORDER' | 'QUOTE' | 'MANUAL' | 'TRANSFER' | 'QUALITY_HOLD';
  quantity: number;
  reservedUntil: Date;
  referenceId?: string;
  referenceType?: string;
  notes?: string;
  status: 'ACTIVE' | 'FULFILLED' | 'CANCELLED' | 'EXPIRED';
  createdAt: Date;
  createdBy: string;
}

const reservationTypes = [
  { value: 'ORDER', label: 'Order', icon: ShoppingCart, color: 'text-green-600' },
  { value: 'QUOTE', label: 'Quote', icon: FileText, color: 'text-blue-600' },
  { value: 'MANUAL', label: 'Manual', icon: User, color: 'text-purple-600' },
  { value: 'TRANSFER', label: 'Transfer', icon: ArrowRightLeft, color: 'text-orange-600' },
  { value: 'QUALITY_HOLD', label: 'Quality Hold', icon: AlertCircle, color: 'text-red-600' }
];

export function InventoryReservationManager({ productId, factoryId, inventoryLocations }: InventoryReservationManagerProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState('');
  const [reservationData, setReservationData] = useState({
    reservationType: 'MANUAL' as const,
    quantity: 0,
    reservedUntil: '',
    referenceId: '',
    referenceType: '',
    notes: ''
  });

  // tRPC queries
  const { 
    data: reservations, 
    refetch: refetchReservations, 
    isLoading: reservationsLoading 
  } = trpc.products.getInventoryReservations?.useQuery({
    productId,
    factoryId
  }) || { data: [], refetch: () => {}, isLoading: false };

  // tRPC mutations
  const createReservationMutation = trpc.products.createInventoryReservation.useMutation({
    onSuccess: () => {
      refetchReservations();
      setShowAddForm(false);
      setSelectedLocation('');
      setReservationData({
        reservationType: 'MANUAL',
        quantity: 0,
        reservedUntil: '',
        referenceId: '',
        referenceType: '',
        notes: ''
      });
    }
  });

  const releaseReservationMutation = trpc.products.releaseInventoryReservation.useMutation({
    onSuccess: () => {
      refetchReservations();
    }
  });

  const handleInputChange = (field: string, value: any) => {
    setReservationData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedLocation) return;

    try {
      await createReservationMutation.mutateAsync({
        inventoryLocationId: selectedLocation,
        reservationData: {
          ...reservationData,
          reservedUntil: new Date(reservationData.reservedUntil).toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to create reservation:', error);
    }
  };

  const handleReleaseReservation = async (reservationId: string, status: 'FULFILLED' | 'CANCELLED') => {
    try {
      await releaseReservationMutation.mutateAsync({
        reservationId,
        status
      });
    } catch (error) {
      console.error('Failed to release reservation:', error);
    }
  };

  const getReservationTypeInfo = (type: string) => {
    return reservationTypes.find(rt => rt.value === type) || reservationTypes[0];
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800';
      case 'FULFILLED': return 'bg-blue-100 text-blue-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      case 'EXPIRED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Lock className="w-5 h-5 mr-2" />
              Inventory Reservations
            </div>
            <Button
              onClick={() => setShowAddForm(true)}
              size="sm"
              className="flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Reservation
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Add Reservation Form */}
          {showAddForm && (
            <Card className="border-2 border-dashed border-blue-300 mb-6">
              <CardHeader>
                <CardTitle className="flex items-center justify-between text-lg">
                  Create Inventory Reservation
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAddForm(false)}
                  >
                    ×
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="location">Location *</Label>
                      <select
                        id="location"
                        value={selectedLocation}
                        onChange={(e) => setSelectedLocation(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        <option value="">Select Location</option>
                        {inventoryLocations.map((location) => (
                          <option key={location.id} value={location.id}>
                            {location.locationName} (Available: {location.availableQuantity})
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="reservationType">Reservation Type *</Label>
                      <select
                        id="reservationType"
                        value={reservationData.reservationType}
                        onChange={(e) => handleInputChange('reservationType', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        {reservationTypes.map((type) => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="quantity">Quantity *</Label>
                      <Input
                        id="quantity"
                        type="number"
                        min="1"
                        value={reservationData.quantity}
                        onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 0)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="reservedUntil">Reserved Until *</Label>
                      <Input
                        id="reservedUntil"
                        type="datetime-local"
                        value={reservationData.reservedUntil}
                        onChange={(e) => handleInputChange('reservedUntil', e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="referenceId">Reference ID</Label>
                      <Input
                        id="referenceId"
                        value={reservationData.referenceId}
                        onChange={(e) => handleInputChange('referenceId', e.target.value)}
                        placeholder="Order ID, Quote ID, etc."
                      />
                    </div>
                    <div>
                      <Label htmlFor="referenceType">Reference Type</Label>
                      <Input
                        id="referenceType"
                        value={reservationData.referenceType}
                        onChange={(e) => handleInputChange('referenceType', e.target.value)}
                        placeholder="Order, Quote, etc."
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      value={reservationData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      rows={3}
                      placeholder="Additional notes about this reservation..."
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={createReservationMutation.isLoading}>
                      {createReservationMutation.isLoading ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        'Create Reservation'
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Reservations List */}
          {reservationsLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mr-2" />
              <span>Loading reservations...</span>
            </div>
          ) : reservations && reservations.length > 0 ? (
            <div className="space-y-3">
              {reservations.map((reservation: any) => {
                const typeInfo = getReservationTypeInfo(reservation.reservationType);
                const Icon = typeInfo.icon;
                const isExpired = new Date(reservation.reservedUntil) < new Date();
                
                return (
                  <Card key={reservation.id} className="hover:shadow-sm transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Icon className={`w-5 h-5 ${typeInfo.color}`} />
                          <div>
                            <div className="flex items-center space-x-2">
                              <span className="font-medium">{typeInfo.label}</span>
                              <Badge className={getStatusColor(reservation.status)}>
                                {reservation.status}
                              </Badge>
                              {isExpired && reservation.status === 'ACTIVE' && (
                                <Badge variant="destructive">Expired</Badge>
                              )}
                            </div>
                            <div className="text-sm text-gray-500">
                              Quantity: {reservation.quantity}
                              {reservation.referenceId && ` • Ref: ${reservation.referenceId}`}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="text-right text-sm">
                            <div className="text-gray-500">
                              Until: {new Date(reservation.reservedUntil).toLocaleDateString()}
                            </div>
                            <div className="text-gray-400">
                              Created: {new Date(reservation.createdAt).toLocaleDateString()}
                            </div>
                          </div>
                          {reservation.status === 'ACTIVE' && (
                            <div className="flex space-x-1">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleReleaseReservation(reservation.id, 'FULFILLED')}
                                disabled={releaseReservationMutation.isLoading}
                              >
                                <CheckCircle className="w-4 h-4 mr-1" />
                                Fulfill
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleReleaseReservation(reservation.id, 'CANCELLED')}
                                disabled={releaseReservationMutation.isLoading}
                              >
                                <XCircle className="w-4 h-4 mr-1" />
                                Cancel
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                      {reservation.notes && (
                        <div className="mt-2 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                          {reservation.notes}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Lock className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No inventory reservations</p>
              <p className="text-sm">Create your first reservation to temporarily allocate inventory</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
