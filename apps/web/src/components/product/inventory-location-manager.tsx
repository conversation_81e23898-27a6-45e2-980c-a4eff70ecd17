'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  MapPin,
  Package,
  AlertTriangle,
  TrendingUp,
  Plus,
  Edit3,
  Trash2,
  Save,
  X,
  BarChart3,
  Clock,
  DollarSign,
  Loader2
} from 'lucide-react';
import { trpc } from '@/lib/trpc';
import { useInventoryLocationValidation } from '@/hooks/use-form-validation';
import { ValidatedInput, ValidatedSelect, ValidatedCheckbox } from '@/components/forms/validated-input';

interface InventoryLocationManagerProps {
  productId: string;
  factoryId: string;
  onLocationsChange?: (locations: any[]) => void;
}

interface InventoryLocation {
  id: string;
  locationName: string;
  locationCode: string | null;
  locationAddress: string | null;
  locationManager: string | null;
  stockQuantity: number;
  reservedQuantity: number;
  availableQuantity: number;
  inTransitQuantity?: number;
  reorderPoint: number | null;
  maxStockLevel: number | null;
  minStockLevel: number | null;
  safetyStock: number | null;
  averageCost: number | null;
  lastCost: number | null;
  costCurrency?: string;
  leadTimeDays?: number | null;
  isActive: boolean;
  allowBackorder?: boolean;
  trackingEnabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface InventoryAlert {
  id: string;
  type: 'LOW_STOCK' | 'OVERSTOCK' | 'REORDER_POINT' | 'STOCKOUT';
  message: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  isAcknowledged: boolean;
  createdAt: Date;
}

export function InventoryLocationManager({ productId, factoryId, onLocationsChange }: InventoryLocationManagerProps) {
  const [editingLocation, setEditingLocation] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newLocation, setNewLocation] = useState({
    locationName: '',
    locationCode: '',
    locationAddress: '',
    locationManager: '',
    stockQuantity: 0,
    reorderPoint: 0,
    maxStockLevel: 0,
    minStockLevel: 0,
    safetyStock: 0,
    averageCost: 0,
    lastCost: 0,
    leadTimeDays: 0,
    allowBackorder: false,
    trackingEnabled: true
  });

  // tRPC queries
  const {
    data: locations,
    refetch: refetchLocations,
    isLoading: locationsLoading
  } = trpc.products.getInventoryLocations.useQuery({
    productId
  });

  const {
    data: alerts,
    refetch: refetchAlerts
  } = trpc.products.getInventoryAlerts.useQuery({});

  const {
    data: analytics
  } = trpc.products.getInventoryAnalytics.useQuery({
    productId
  });

  // tRPC mutations
  const updateLocationMutation = trpc.products.updateInventoryLocation.useMutation({
    onSuccess: () => {
      refetchLocations();
      refetchAlerts();
      setEditingLocation(null);
      setShowAddForm(false);
      setNewLocation({
        locationName: '',
        locationCode: '',
        locationAddress: '',
        locationManager: '',
        stockQuantity: 0,
        reorderPoint: 0,
        maxStockLevel: 0,
        minStockLevel: 0,
        safetyStock: 0,
        averageCost: 0,
        lastCost: 0,
        leadTimeDays: 0,
        allowBackorder: false,
        trackingEnabled: true
      });
    }
  });

  const acknowledgeAlertMutation = trpc.products.acknowledgeInventoryAlert.useMutation({
    onSuccess: () => {
      refetchAlerts();
    }
  });

  // Update parent component when locations change
  useEffect(() => {
    if (locations && onLocationsChange) {
      onLocationsChange(locations.locations);
    }
  }, [locations, onLocationsChange]);

  const handleSaveLocation = async (locationData: any, locationId?: string) => {
    try {
      await updateLocationMutation.mutateAsync({
        productId,
        locationId,
        locationData: {
          ...locationData,
          costCurrency: 'USD' // Default currency
        }
      });
    } catch (error) {
      console.error('Failed to save location:', error);
    }
  };

  const handleAcknowledgeAlert = async (alertId: string) => {
    try {
      await acknowledgeAlertMutation.mutateAsync({
        alertId,
        factoryId
      });
    } catch (error) {
      console.error('Failed to acknowledge alert:', error);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (locationsLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin mr-2" />
            <span>Loading inventory locations...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <MapPin className="w-5 h-5 mr-2" />
              Multi-Location Inventory Management
            </div>
            <Button
              onClick={() => setShowAddForm(true)}
              size="sm"
              className="flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Location
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="locations" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="locations">Locations</TabsTrigger>
              <TabsTrigger value="alerts">Alerts ({alerts?.alerts?.filter((a: any) => !a.isAcknowledged).length || 0})</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="locations" className="space-y-4">
              {/* Add New Location Form */}
              {showAddForm && (
                <Card className="border-2 border-dashed border-blue-300">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between text-lg">
                      Add New Location
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowAddForm(false)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <LocationForm
                      location={newLocation}
                      onSave={(data) => handleSaveLocation(data)}
                      onCancel={() => setShowAddForm(false)}
                      isLoading={updateLocationMutation.isLoading}
                    />
                  </CardContent>
                </Card>
              )}

              {/* Existing Locations */}
              {locations && locations.locations && locations.locations.length > 0 ? (
                <div className="grid gap-4">
                  {locations.locations.map((location: any) => (
                    <LocationCard
                      key={location.id}
                      location={location}
                      isEditing={editingLocation === location.id}
                      onEdit={() => setEditingLocation(location.id)}
                      onSave={(data) => handleSaveLocation(data, location.id)}
                      onCancel={() => setEditingLocation(null)}
                      isLoading={updateLocationMutation.isLoading}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Package className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No inventory locations configured</p>
                  <p className="text-sm">Add your first location to start tracking inventory</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="alerts" className="space-y-4">
              {alerts && alerts.alerts && alerts.alerts.length > 0 ? (
                <div className="space-y-3">
                  {alerts.alerts.map((alert: any) => (
                    <Alert key={alert.id} className={getSeverityColor(alert.severity)}>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{alert.message}</p>
                          <p className="text-xs opacity-75 mt-1">
                            {new Date(alert.createdAt).toLocaleString()}
                          </p>
                        </div>
                        {!alert.isAcknowledged && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleAcknowledgeAlert(alert.id)}
                            disabled={acknowledgeAlertMutation.isLoading}
                          >
                            Acknowledge
                          </Button>
                        )}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <AlertTriangle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No active alerts</p>
                  <p className="text-sm">Your inventory levels are within normal ranges</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              {analytics ? (
                <AnalyticsPanel analytics={analytics} />
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <BarChart3 className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>Analytics not available</p>
                  <p className="text-sm">Add inventory locations to see analytics</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

// LocationForm component for adding/editing locations
interface LocationFormProps {
  location: any;
  onSave: (data: any) => void;
  onCancel: () => void;
  isLoading: boolean;
}

function LocationForm({ location, onSave, onCancel, isLoading }: LocationFormProps) {
  const initialValues = {
    locationName: location?.locationName || '',
    locationCode: location?.locationCode || '',
    locationAddress: location?.locationAddress || '',
    locationManager: location?.locationManager || '',
    stockQuantity: location?.stockQuantity || 0,
    reorderPoint: location?.reorderPoint || 0,
    maxStockLevel: location?.maxStockLevel || 0,
    minStockLevel: location?.minStockLevel || 0,
    safetyStock: location?.safetyStock || 0,
    averageCost: location?.averageCost || 0,
    lastCost: location?.lastCost || 0,
    leadTime: location?.leadTime || 0,
    allowBackorders: location?.allowBackorders || false,
    trackingEnabled: location?.trackingEnabled !== false,
  };

  const {
    values,
    errors,
    isValid,
    setValue,
    validateAll,
    getFieldError,
    hasFieldError
  } = useInventoryLocationValidation(initialValues);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const isFormValid = await validateAll();
    if (isFormValid) {
      onSave(values);
    }
  };

  const getValidationStatus = (field: string) => ({
    isInvalid: hasFieldError(field as any),
    message: getFieldError(field as any),
  });

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ValidatedInput
          label="Location Name"
          required
          value={values.locationName || ''}
          onChange={(value) => setValue('locationName', value)}
          validation={getValidationStatus('locationName')}
          placeholder="Enter location name"
        />

        <ValidatedInput
          label="Location Code"
          value={values.locationCode || ''}
          onChange={(value) => setValue('locationCode', value)}
          validation={getValidationStatus('locationCode')}
          placeholder="Enter location code"
          description="Optional unique identifier for this location"
        />

        <ValidatedInput
          label="Address"
          value={values.locationAddress || ''}
          onChange={(value) => setValue('locationAddress', value)}
          validation={getValidationStatus('locationAddress')}
          placeholder="Enter location address"
        />

        <ValidatedInput
          label="Manager"
          value={values.locationManager || ''}
          onChange={(value) => setValue('locationManager', value)}
          validation={getValidationStatus('locationManager')}
          placeholder="Enter manager name"
        />
        <ValidatedInput
          label="Stock Quantity"
          required
          type="number"
          min={0}
          value={values.stockQuantity || 0}
          onChange={(value) => setValue('stockQuantity', Number(value))}
          validation={getValidationStatus('stockQuantity')}
          placeholder="0"
          description="Current stock quantity at this location"
        />

        <ValidatedInput
          label="Reorder Point"
          type="number"
          min={0}
          value={values.reorderPoint || 0}
          onChange={(value) => setValue('reorderPoint', Number(value))}
          validation={getValidationStatus('reorderPoint')}
          placeholder="0"
          description="Trigger reorder when stock reaches this level"
        />

        <ValidatedInput
          label="Max Stock Level"
          type="number"
          min={0}
          value={values.maxStockLevel || 0}
          onChange={(value) => setValue('maxStockLevel', Number(value))}
          validation={getValidationStatus('maxStockLevel')}
          placeholder="0"
          description="Maximum stock capacity for this location"
        />

        <ValidatedInput
          label="Min Stock Level"
          type="number"
          min={0}
          value={values.minStockLevel || 0}
          onChange={(value) => setValue('minStockLevel', Number(value))}
          validation={getValidationStatus('minStockLevel')}
          placeholder="0"
          description="Minimum stock level to maintain"
        />
        <ValidatedInput
          label="Safety Stock"
          type="number"
          min={0}
          value={values.safetyStock || 0}
          onChange={(value) => setValue('safetyStock', Number(value))}
          validation={getValidationStatus('safetyStock')}
          placeholder="0"
          description="Buffer stock for unexpected demand"
        />

        <ValidatedInput
          label="Average Cost"
          type="number"
          step={0.01}
          min={0}
          value={values.averageCost || 0}
          onChange={(value) => setValue('averageCost', Number(value))}
          validation={getValidationStatus('averageCost')}
          placeholder="0.00"
          description="Average cost per unit"
        />

        <ValidatedInput
          label="Last Cost"
          type="number"
          step={0.01}
          min={0}
          value={values.lastCost || 0}
          onChange={(value) => setValue('lastCost', Number(value))}
          validation={getValidationStatus('lastCost')}
          placeholder="0.00"
          description="Most recent purchase cost per unit"
        />

        <ValidatedInput
          label="Lead Time (Days)"
          type="number"
          min={0}
          value={values.leadTime || 0}
          onChange={(value) => setValue('leadTime', Number(value))}
          validation={getValidationStatus('leadTime')}
          placeholder="0"
          description="Days to replenish stock"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ValidatedCheckbox
          label="Allow Backorders"
          checked={values.allowBackorders || false}
          onChange={(checked) => setValue('allowBackorders', checked)}
          validation={getValidationStatus('allowBackorders')}
          description="Allow orders when stock is insufficient"
        />

        <ValidatedCheckbox
          label="Enable Tracking"
          checked={values.trackingEnabled !== false}
          onChange={(checked) => setValue('trackingEnabled', checked)}
          validation={getValidationStatus('trackingEnabled')}
          description="Track inventory movements and alerts"
        />
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isLoading || !isValid}
          className={!isValid ? 'opacity-50' : ''}
        >
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              Save Location
            </>
          )}
        </Button>
      </div>

      {/* Validation summary */}
      {Object.keys(errors).length > 0 && (
        <Alert className="mt-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Please fix the following errors before saving:
            <ul className="list-disc list-inside mt-2">
              {Object.entries(errors).map(([field, message]) => (
                <li key={field} className="text-sm">
                  {message}
                </li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}
    </form>
  );
}

// LocationCard component for displaying location information
interface LocationCardProps {
  location: InventoryLocation;
  isEditing: boolean;
  onEdit: () => void;
  onSave: (data: any) => void;
  onCancel: () => void;
  isLoading: boolean;
}

function LocationCard({ location, isEditing, onEdit, onSave, onCancel, isLoading }: LocationCardProps) {
  if (isEditing) {
    return (
      <Card className="border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center justify-between text-lg">
            Edit Location: {location.locationName}
            <Button variant="ghost" size="sm" onClick={onCancel}>
              <X className="w-4 h-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LocationForm
            location={{
              locationName: location.locationName,
              locationCode: location.locationCode || '',
              locationAddress: location.locationAddress || '',
              locationManager: location.locationManager || '',
              stockQuantity: location.stockQuantity,
              reorderPoint: location.reorderPoint || 0,
              maxStockLevel: location.maxStockLevel || 0,
              minStockLevel: location.minStockLevel || 0,
              safetyStock: location.safetyStock || 0,
              averageCost: location.averageCost || 0,
              lastCost: location.lastCost || 0,
              leadTimeDays: location.leadTimeDays || 0,
              allowBackorder: location.allowBackorder,
              trackingEnabled: location.trackingEnabled
            }}
            onSave={onSave}
            onCancel={onCancel}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <MapPin className="w-5 h-5 mr-2" />
            <div>
              <h3 className="font-semibold">{location.locationName}</h3>
              {location.locationCode && (
                <p className="text-sm text-gray-500">Code: {location.locationCode}</p>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={location.isActive ? "default" : "secondary"}>
              {location.isActive ? "Active" : "Inactive"}
            </Badge>
            <Button variant="ghost" size="sm" onClick={onEdit}>
              <Edit3 className="w-4 h-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{location.stockQuantity}</div>
            <div className="text-sm text-gray-500">Stock</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{location.availableQuantity}</div>
            <div className="text-sm text-gray-500">Available</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{location.reservedQuantity}</div>
            <div className="text-sm text-gray-500">Reserved</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{location.inTransitQuantity}</div>
            <div className="text-sm text-gray-500">In Transit</div>
          </div>
        </div>

        {(location.locationAddress || location.locationManager) && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            {location.locationAddress && (
              <p className="text-sm text-gray-600 mb-1">
                <strong>Address:</strong> {location.locationAddress}
              </p>
            )}
            {location.locationManager && (
              <p className="text-sm text-gray-600">
                <strong>Manager:</strong> {location.locationManager}
              </p>
            )}
          </div>
        )}

        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            {location.reorderPoint && (
              <div>
                <span className="text-gray-500">Reorder Point:</span>
                <span className="ml-1 font-medium">{location.reorderPoint}</span>
              </div>
            )}
            {location.maxStockLevel && (
              <div>
                <span className="text-gray-500">Max Stock:</span>
                <span className="ml-1 font-medium">{location.maxStockLevel}</span>
              </div>
            )}
            {location.minStockLevel && (
              <div>
                <span className="text-gray-500">Min Stock:</span>
                <span className="ml-1 font-medium">{location.minStockLevel}</span>
              </div>
            )}
            {location.safetyStock && (
              <div>
                <span className="text-gray-500">Safety Stock:</span>
                <span className="ml-1 font-medium">{location.safetyStock}</span>
              </div>
            )}
            {location.averageCost && (
              <div>
                <span className="text-gray-500">Avg Cost:</span>
                <span className="ml-1 font-medium">${location.averageCost.toFixed(2)}</span>
              </div>
            )}
            {location.lastCost && (
              <div>
                <span className="text-gray-500">Last Cost:</span>
                <span className="ml-1 font-medium">${location.lastCost.toFixed(2)}</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// AnalyticsPanel component for displaying inventory analytics
interface AnalyticsPanelProps {
  analytics: any;
}

function AnalyticsPanel({ analytics }: AnalyticsPanelProps) {
  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{analytics.totalStock || 0}</div>
            <div className="text-sm text-gray-500">Total Stock</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{analytics.totalAvailable || 0}</div>
            <div className="text-sm text-gray-500">Available</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{analytics.totalReserved || 0}</div>
            <div className="text-sm text-gray-500">Reserved</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">
              ${analytics.totalValue?.toFixed(2) || '0.00'}
            </div>
            <div className="text-sm text-gray-500">Total Value</div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            Performance Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-xl font-semibold">{analytics.turnoverRate?.toFixed(2) || 'N/A'}</div>
              <div className="text-sm text-gray-500">Inventory Turnover</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-semibold">{analytics.daysOfInventory?.toFixed(0) || 'N/A'}</div>
              <div className="text-sm text-gray-500">Days of Inventory</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-semibold">{analytics.stockoutRisk || 'Low'}</div>
              <div className="text-sm text-gray-500">Stockout Risk</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Location Performance */}
      {analytics.locationPerformance && analytics.locationPerformance.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="w-5 h-5 mr-2" />
              Location Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.locationPerformance.map((location: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium">{location.locationName}</div>
                    <div className="text-sm text-gray-500">
                      Stock: {location.stockQuantity} | Available: {location.availableQuantity}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">${location.value?.toFixed(2) || '0.00'}</div>
                    <div className="text-sm text-gray-500">Value</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
