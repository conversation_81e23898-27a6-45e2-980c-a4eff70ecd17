'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '../../contexts/auth-context';
import { Button } from '../ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Badge } from '../ui/badge';
import {
  BarChart3,
  Package,
  ShoppingCart,
  MessageSquare,
  Users,
  TrendingUp,
  Settings,
  Factory,
  LogOut,
  Menu,
  X,
  Activity,
  FileText,
  Copy,
  RefreshCw,
  BookOpen
} from 'lucide-react';

export function DashboardSidebar() {
  const { user, payloadUser, logout } = useAuth();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navigation = [
    { name: 'Overview', icon: BarChart3, href: '/dashboard', current: pathname === '/dashboard' },
    { name: 'Products', icon: Package, href: '/dashboard/products', current: pathname.startsWith('/dashboard/products') },
    { name: 'Templates', icon: Copy, href: '/dashboard/templates', current: pathname.startsWith('/dashboard/templates') },
    { name: 'Template Library', icon: BookOpen, href: '/dashboard/marketplace', current: pathname.startsWith('/dashboard/marketplace') },
    { name: 'Recurring Orders', icon: RefreshCw, href: '/dashboard/recurring-orders', current: pathname.startsWith('/dashboard/recurring-orders') },
    { name: 'Quotes', icon: FileText, href: '/dashboard/quotes', current: pathname.startsWith('/dashboard/quotes') },
    { name: 'Orders', icon: ShoppingCart, href: '/dashboard/orders', current: pathname.startsWith('/dashboard/orders') },
    { name: 'Messages', icon: MessageSquare, href: '/dashboard/messages', current: pathname.startsWith('/dashboard/messages') },
    { name: 'Customers', icon: Users, href: '/dashboard/customers', current: pathname.startsWith('/dashboard/customers') },
    { name: 'Analytics', icon: TrendingUp, href: '/dashboard/analytics', current: pathname.startsWith('/dashboard/analytics') },
    { name: 'Profile', icon: Settings, href: '/dashboard/profile', current: pathname.startsWith('/dashboard/profile') },
  ];

  const SidebarContent = () => (
    <div className="flex flex-col h-full">
      {/* Logo */}
      <div className="flex items-center h-16 px-4 border-b">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <Factory className="w-5 h-5 text-white" />
          </div>
          <span className="text-lg font-bold">FC-CHINA</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-1">
        {navigation.map((item) => (
          <Link
            key={item.name}
            href={item.href}
            className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              item.current
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
            }`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            <item.icon className="w-5 h-5 mr-3" />
            {item.name}
          </Link>
        ))}
      </nav>

      {/* User Profile Section */}
      <div className="border-t border-gray-200 p-4">
        <div className="flex items-center space-x-3 mb-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={payloadUser?.avatar || undefined} alt={payloadUser?.firstName || undefined} />
            <AvatarFallback>
              {payloadUser?.firstName?.[0]}{payloadUser?.lastName?.[0]}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {payloadUser?.firstName} {payloadUser?.lastName}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {user?.email}
            </p>
          </div>
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Activity className="w-2 h-2 text-green-500" />
            <span className="text-xs">Online</span>
          </Badge>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          className="w-full justify-start"
          onClick={logout}
        >
          <LogOut className="w-4 h-4 mr-2" />
          Sign Out
        </Button>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
        </Button>
      </div>

      {/* Mobile sidebar overlay */}
      {isMobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-40 flex">
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setIsMobileMenuOpen(false)} />
          <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
            <SidebarContent />
          </div>
        </div>
      )}

      {/* Desktop sidebar */}
      <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0">
        <div className="flex flex-col flex-grow bg-white border-r border-gray-200">
          <SidebarContent />
        </div>
      </div>
    </>
  );
}
