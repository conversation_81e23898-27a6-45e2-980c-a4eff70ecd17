'use client';

import { useAuth } from '../../contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import Link from 'next/link';
import {
  Package,
  ShoppingCart,
  MessageSquare,
  Users,
  TrendingUp,
  Globe,
  Plus,
  ArrowUpRight,
  Activity,
  Settings
} from 'lucide-react';

export function DashboardOverview() {
  const { user, payloadUser, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Mock data - in production, this would come from API calls
  const stats = [
    {
      name: 'Total Products',
      value: '248',
      change: '+12%',
      changeType: 'positive' as const,
      icon: Package,
      description: 'Active products in catalog'
    },
    {
      name: 'Active Orders',
      value: '32',
      change: '+8%',
      changeType: 'positive' as const,
      icon: ShoppingCart,
      description: 'Orders in progress'
    },
    {
      name: 'New Messages',
      value: '15',
      change: '+3%',
      changeType: 'positive' as const,
      icon: MessageSquare,
      description: 'Unread customer messages'
    },
    {
      name: 'Monthly Revenue',
      value: '$24,500',
      change: '+18%',
      changeType: 'positive' as const,
      icon: TrendingUp,
      description: 'Revenue this month'
    },
  ];

  const recentActivity = [
    { id: 1, type: 'order', message: 'New order from Global Tech Corp', time: '2 hours ago', status: 'new' },
    { id: 2, type: 'product', message: 'Product "Industrial Pump" updated', time: '4 hours ago', status: 'updated' },
    { id: 3, type: 'message', message: 'Message from European Distributor', time: '6 hours ago', status: 'unread' },
    { id: 4, type: 'customer', message: 'New customer registration', time: '1 day ago', status: 'new' },
  ];

  const quickActions = [
    { name: 'Add Product', icon: Package, description: 'Add new product to catalog' },
    { name: 'New Message', icon: MessageSquare, description: 'Send message to customer' },
    { name: 'Manage Team', icon: Users, description: 'Manage factory team members' },
    { name: 'View Public Profile', icon: Globe, description: 'View your public factory profile' },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Avatar className="h-12 w-12">
            <AvatarImage src={payloadUser?.avatar || undefined} alt={payloadUser?.firstName || undefined} />
            <AvatarFallback>
              {payloadUser?.firstName?.[0]}{payloadUser?.lastName?.[0]}
            </AvatarFallback>
          </Avatar>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back, {payloadUser?.firstName}!
            </h1>
            <p className="text-gray-600">
              Here's what's happening with your factory today.
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Activity className="w-3 h-3 text-green-500" />
            <span>Online</span>
          </Badge>
          <Link href="/onboarding">
            <Button>
              <Settings className="w-4 h-4 mr-2" />
              Factory Setup Wizard
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <Card key={stat.name} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.name}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
              <div className="flex items-center text-sm mt-1">
                <span className="text-green-600 font-medium flex items-center">
                  <ArrowUpRight className="w-3 h-3 mr-1" />
                  {stat.change}
                </span>
                <span className="text-gray-500 ml-1">from last month</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Activity - Takes 2 columns */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest updates from your factory</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className={`w-2 h-2 rounded-full ${
                      activity.status === 'new' ? 'bg-blue-500' :
                      activity.status === 'updated' ? 'bg-green-500' :
                      activity.status === 'unread' ? 'bg-orange-500' :
                      'bg-gray-400'
                    }`} />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {activity.message}
                      </p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <ArrowUpRight className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions - Takes 1 column */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common tasks and shortcuts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {quickActions.map((action) => (
                  <Button
                    key={action.name}
                    variant="outline"
                    className="w-full justify-start h-auto p-4"
                  >
                    <action.icon className="w-5 h-5 mr-3" />
                    <div className="text-left">
                      <div className="font-medium">{action.name}</div>
                      <div className="text-xs text-gray-500">{action.description}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
