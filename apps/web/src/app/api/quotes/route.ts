import { NextRequest, NextResponse } from 'next/server';
import { prisma, ensurePrismaConnection, withDatabaseRetry } from '../../../lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Ensure database connection
    await ensurePrismaConnection();

    const { searchParams } = new URL(request.url);
    const factoryId = searchParams.get('factoryId');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const query = searchParams.get('query');

    if (!factoryId) {
      return NextResponse.json(
        { error: 'Factory ID is required' },
        { status: 400 }
      );
    }

    const skip = (page - 1) * limit;

    // Build where clause
    const whereClause: any = {
      factoryId,
    };

    if (status) {
      whereClause.status = status;
    }

    if (query) {
      whereClause.OR = [
        { quoteNumber: { contains: query, mode: 'insensitive' } },
        { customerName: { contains: query, mode: 'insensitive' } },
        { customerEmail: { contains: query, mode: 'insensitive' } },
        { customerCompany: { contains: query, mode: 'insensitive' } },
      ];
    }

    // Use retry wrapper for database operations
    const result = await withDatabaseRetry(async () => {
      // Get quotes with related data
      const [quotes, total] = await Promise.all([
        prisma.quote.findMany({
          where: whereClause,
          include: {
            items: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    sku: true,
                    basePrice: true,
                  }
                },
                variant: {
                  select: {
                    id: true,
                    name: true,
                    sku: true,
                  }
                }
              }
            },
            factory: {
              select: {
                id: true,
                name: true,
                slug: true,
              }
            },
            order: {
              select: {
                id: true,
                orderNumber: true,
                status: true,
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        prisma.quote.count({ where: whereClause })
      ]);

      return { quotes, total };
    }, 'Quotes retrieval');

    console.log('✅ Quotes retrieved successfully:', {
      factoryId,
      count: result.quotes.length,
      total: result.total,
      page,
      limit
    });

    return NextResponse.json({
      quotes: result.quotes,
      pagination: {
        page,
        limit,
        total: result.total,
        pages: Math.ceil(result.total / limit)
      }
    });

  } catch (error: any) {
    console.error('❌ Error retrieving quotes:', error);

    // Provide more specific error messages
    let errorMessage = 'Failed to retrieve quotes';
    if (error.message?.includes('connection')) {
      errorMessage = 'Database connection error. Please try again.';
    } else if (error.message?.includes('timeout')) {
      errorMessage = 'Request timeout. Please try again.';
    }

    return NextResponse.json(
      { error: errorMessage, details: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Ensure database connection
    await ensurePrismaConnection();

    const body = await request.json();
    
    console.log('🔄 Creating quote:', {
      customerName: body.customerName,
      factoryId: body.factoryId,
      itemsCount: body.items?.length || 0,
    });

    // Validate required fields
    if (!body.customerName || !body.customerEmail || !body.factoryId || !body.items || body.items.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields: customerName, customerEmail, factoryId, items' },
        { status: 400 }
      );
    }

    // Generate unique quote number
    const quoteNumber = `QT-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

    // Calculate totals
    let subtotal = 0;
    const validatedItems: any[] = [];

    for (const item of body.items) {
      if (!item.productId || !item.quantity || !item.unitPrice) {
        return NextResponse.json(
          { error: 'Each item must have productId, quantity, and unitPrice' },
          { status: 400 }
        );
      }

      // Verify product exists and belongs to factory
      const product = await prisma.product.findFirst({
        where: {
          id: item.productId,
          factoryId: body.factoryId
        }
      });

      if (!product) {
        return NextResponse.json(
          { error: `Product ${item.productId} not found or does not belong to factory` },
          { status: 400 }
        );
      }

      const totalPrice = parseFloat(item.unitPrice) * parseInt(item.quantity);
      subtotal += totalPrice;

      validatedItems.push({
        productId: item.productId,
        variantId: item.variantId || null,
        quantity: parseInt(item.quantity),
        unitPrice: parseFloat(item.unitPrice),
        totalPrice,
        notes: item.notes || null,
      });
    }

    const tax = body.tax ? parseFloat(body.tax) : 0;
    const shipping = body.shipping ? parseFloat(body.shipping) : 0;
    const total = subtotal + tax + shipping;

    // Set validity period (default 30 days)
    const validUntil = new Date();
    validUntil.setDate(validUntil.getDate() + (body.validityDays || 30));

    // Use retry wrapper for database operations
    const quote = await withDatabaseRetry(async () => {
      return await prisma.quote.create({
        data: {
          quoteNumber,
          status: 'PENDING',
          subtotal,
          tax,
          shipping,
          total,
          currency: body.currency || 'USD',
          validUntil,
          customerName: body.customerName,
          customerEmail: body.customerEmail,
          customerPhone: body.customerPhone || null,
          customerCompany: body.customerCompany || null,
          shippingAddress: body.shippingAddress || null,
          shippingMethod: body.shippingMethod || null,
          factoryId: body.factoryId,
          // createdBy field omitted - will be added later when user management is implemented
          items: {
            create: validatedItems
          }
        },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                }
              },
              variant: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                }
              }
            }
          },
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
            }
          }
        }
      });
    }, 'Quote creation');

    console.log('✅ Quote created successfully:', {
      id: quote.id,
      quoteNumber: quote.quoteNumber,
      total: quote.total,
      itemsCount: quote.items.length,
    });

    return NextResponse.json(quote, { status: 201 });

  } catch (error) {
    console.error('❌ Error creating quote:', error);
    return NextResponse.json(
      { error: 'Failed to create quote' },
      { status: 500 }
    );
  }
}
