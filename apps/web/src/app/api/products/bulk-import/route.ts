import { NextRequest, NextResponse } from 'next/server';
import { parse } from 'csv-parse/sync';
import { processProductImages, createProductImageRecords } from '../../../../utils/image-upload';

// Database connection utility
async function ensurePrismaConnection() {
  const { PrismaClient } = await import('@prisma/client');
  const prisma = new PrismaClient();
  
  try {
    await prisma.$connect();
    return prisma;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw new Error('Database connection failed');
  }
}

// Helper function to validate and transform CSV record to product data
async function validateAndTransformRecord(
  record: any,
  rowIndex: number,
  categoryMap: Map<string, string>,
  factoryId: string
): Promise<{
  productData: any;
  imageUrls: string[];
  mainImageUrl: string | null;
}> {
  const errors: string[] = [];

  // Required fields validation
  if (!record.name || typeof record.name !== 'string' || record.name.trim().length === 0) {
    errors.push('name is required');
  }

  if (!record.basePrice || isNaN(parseFloat(record.basePrice))) {
    errors.push('basePrice must be a valid number');
  }

  if (!record.categoryName || typeof record.categoryName !== 'string') {
    errors.push('categoryName is required');
  }

  // Validate category exists
  const categoryId = categoryMap.get(record.categoryName?.toLowerCase());
  if (!categoryId) {
    errors.push(`Category "${record.categoryName}" not found. Please create the category first.`);
  }

  if (errors.length > 0) {
    throw new Error(`Row ${rowIndex}: ${errors.join(', ')}`);
  }

  // Generate slug from name
  const slug = record.name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '')
    .substring(0, 50);

  // Transform and validate data
  const productData = {
    name: record.name.trim(),
    slug: `${slug}-${Date.now()}-${Math.random().toString(36).substring(7)}`, // Ensure uniqueness
    description: record.description?.trim() || null,
    shortDescription: record.shortDescription?.trim() || null,
    basePrice: parseFloat(record.basePrice),
    currency: record.currency || 'USD',
    minOrderQty: record.minOrderQty ? parseInt(record.minOrderQty) : 1,
    maxOrderQty: record.maxOrderQty ? parseInt(record.maxOrderQty) : null,
    sku: record.sku?.trim() || null,
    model: record.model?.trim() || null,
    brand: record.brand?.trim() || null,
    weight: record.weight ? parseFloat(record.weight) : null,
    materials: record.materials ? record.materials.split(',').map((m: string) => m.trim()) : [],
    colors: record.colors ? record.colors.split(',').map((c: string) => c.trim()) : [],
    tags: record.tags ? record.tags.split(',').map((t: string) => t.trim()) : [],
    stockQuantity: record.stockQuantity ? parseInt(record.stockQuantity) : 0,
    categoryId: categoryId!,
    metaTitle: record.metaTitle?.trim() || null,
    metaDescription: record.metaDescription?.trim() || null,
    status: 'ACTIVE' as const,
    isActive: true,
    isFeatured: false,
  };

  // Additional validations
  if (productData.basePrice <= 0) {
    throw new Error(`Row ${rowIndex}: basePrice must be greater than 0`);
  }

  if (productData.minOrderQty <= 0) {
    throw new Error(`Row ${rowIndex}: minOrderQty must be greater than 0`);
  }

  if (productData.maxOrderQty !== null && productData.maxOrderQty < productData.minOrderQty) {
    throw new Error(`Row ${rowIndex}: maxOrderQty must be greater than or equal to minOrderQty`);
  }

  if (productData.weight !== null && productData.weight < 0) {
    throw new Error(`Row ${rowIndex}: weight must be non-negative`);
  }

  if (productData.stockQuantity < 0) {
    throw new Error(`Row ${rowIndex}: stockQuantity must be non-negative`);
  }

  // Parse image URLs
  const imageUrls = record.imageUrls ?
    record.imageUrls.split(',').map((url: string) => url.trim()).filter((url: string) => url.length > 0) :
    [];
  const mainImageUrl = record.mainImageUrl?.trim() || null;

  return { productData, imageUrls, mainImageUrl };
}

export async function POST(request: NextRequest) {
  let prisma;
  
  try {
    prisma = await ensurePrismaConnection();
    
    const body = await request.json();
    const { csvData, options = {} } = body;
    
    console.log('🔄 Processing bulk import request:', {
      csvDataLength: csvData?.length,
      options,
    });

    if (!csvData || typeof csvData !== 'string') {
      return NextResponse.json(
        { error: 'CSV data is required' },
        { status: 400 }
      );
    }

    const { skipErrors = true, validateOnly = false, updateExisting = false, batchSize = 50 } = options;

    // Parse CSV data
    const records = parse(csvData, {
      columns: true,
      skip_empty_lines: true,
      trim: true,
    });

    if (records.length === 0) {
      return NextResponse.json(
        { error: 'CSV file is empty or has no valid records' },
        { status: 400 }
      );
    }

    if (records.length > 1000) {
      return NextResponse.json(
        { error: 'Maximum 1000 products allowed per import' },
        { status: 400 }
      );
    }

    // For now, use a hardcoded factory ID - in production this would come from authentication
    const factoryId = 'cmdgtue8w0000neikaoycszlo'; // This should come from authenticated user

    const results = {
      totalRecords: records.length,
      processedRecords: 0,
      successCount: 0,
      errorCount: 0,
      errors: [] as Array<{
        row: number;
        field?: string;
        message: string;
        data: any;
      }>,
      createdProducts: [] as any[],
    };

    // Get factory categories for validation
    const factoryCategories = await prisma.category.findMany({
      where: { factoryId },
      select: { id: true, name: true },
    });

    const categoryMap = new Map(
      factoryCategories.map(cat => [cat.name.toLowerCase(), cat.id])
    );

    console.log('📋 Available categories:', Array.from(categoryMap.keys()));

    // Process records in batches
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize);
      
      for (let j = 0; j < batch.length; j++) {
        const rowIndex = i + j + 2; // +2 for header row and 1-based indexing
        const record = batch[j];
        
        try {
          // Validate and transform record
          const { productData, imageUrls, mainImageUrl } = await validateAndTransformRecord(
            record,
            rowIndex,
            categoryMap,
            factoryId
          );

          if (!validateOnly) {
            // Check if product exists (by SKU or name)
            let existingProduct = null;
            if (productData.sku) {
              existingProduct = await prisma.product.findFirst({
                where: {
                  factoryId,
                  sku: productData.sku,
                },
              });
            }

            if (!existingProduct) {
              existingProduct = await prisma.product.findFirst({
                where: {
                  factoryId,
                  name: productData.name,
                },
              });
            }

            if (existingProduct && !updateExisting) {
              results.errors.push({
                row: rowIndex,
                message: `Product already exists: ${productData.name}`,
                data: record,
              });
              results.errorCount++;
            } else if (existingProduct && updateExisting) {
              // Update existing product
              const updatedProduct = await prisma.product.update({
                where: { id: existingProduct.id },
                data: productData,
                include: {
                  category: true,
                  factory: {
                    select: { id: true, name: true, slug: true },
                  },
                },
              });

              // Process images if provided
              if (imageUrls.length > 0) {
                try {
                  const { images, errors: imageErrors } = await processProductImages(
                    imageUrls,
                    mainImageUrl,
                    factoryId,
                    updatedProduct.id
                  );

                  if (images.length > 0) {
                    // Delete existing images first
                    await prisma.productImage.deleteMany({
                      where: { productId: updatedProduct.id },
                    });

                    // Create new image records
                    await createProductImageRecords(updatedProduct.id, images, prisma);
                  }

                  // Add image errors to results
                  if (imageErrors.length > 0) {
                    results.errors.push({
                      row: rowIndex,
                      message: `Image processing errors: ${imageErrors.map(e => e.error).join(', ')}`,
                      data: record,
                    });
                  }
                } catch (imageError) {
                  console.error(`❌ Image processing failed for product ${updatedProduct.id}:`, imageError);
                  results.errors.push({
                    row: rowIndex,
                    message: `Image processing failed: ${imageError instanceof Error ? imageError.message : 'Unknown error'}`,
                    data: record,
                  });
                }
              }

              results.createdProducts.push(updatedProduct);
              results.successCount++;
            } else {
              // Create new product
              const newProduct = await prisma.product.create({
                data: {
                  ...productData,
                  factoryId,
                },
                include: {
                  category: true,
                  factory: {
                    select: { id: true, name: true, slug: true },
                  },
                },
              });

              // Process images if provided
              if (imageUrls.length > 0) {
                try {
                  const { images, errors: imageErrors } = await processProductImages(
                    imageUrls,
                    mainImageUrl,
                    factoryId,
                    newProduct.id
                  );

                  if (images.length > 0) {
                    await createProductImageRecords(newProduct.id, images, prisma);
                  }

                  // Add image errors to results
                  if (imageErrors.length > 0) {
                    results.errors.push({
                      row: rowIndex,
                      message: `Image processing errors: ${imageErrors.map(e => e.error).join(', ')}`,
                      data: record,
                    });
                  }
                } catch (imageError) {
                  console.error(`❌ Image processing failed for product ${newProduct.id}:`, imageError);
                  results.errors.push({
                    row: rowIndex,
                    message: `Image processing failed: ${imageError instanceof Error ? imageError.message : 'Unknown error'}`,
                    data: record,
                  });
                }
              }

              results.createdProducts.push(newProduct);
              results.successCount++;
            }
          } else {
            // Validation only mode
            results.successCount++;
          }

          results.processedRecords++;
        } catch (error) {
          results.errorCount++;
          results.errors.push({
            row: rowIndex,
            message: error instanceof Error ? error.message : 'Unknown error',
            data: record,
          });

          if (!skipErrors) {
            throw error;
          }
        }
      }
    }

    console.log('✅ Bulk import completed:', {
      totalRecords: results.totalRecords,
      successCount: results.successCount,
      errorCount: results.errorCount,
    });

    return NextResponse.json(results);

  } catch (error) {
    console.error('❌ Bulk import failed:', error);
    return NextResponse.json(
      { error: `Bulk import failed: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  } finally {
    if (prisma) {
      await prisma.$disconnect();
    }
  }
}
