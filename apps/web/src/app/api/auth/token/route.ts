import { getAccessToken } from '@auth0/nextjs-auth0';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const accessTokenResult = await getAccessToken();

    if (!accessTokenResult) {
      return NextResponse.json({ error: 'No access token available' }, { status: 401 });
    }

    // Handle both string and object return types from Auth0
    const accessToken = typeof accessTokenResult === 'string'
      ? accessTokenResult
      : (accessTokenResult as any).accessToken;

    return NextResponse.json({ accessToken });
  } catch (error) {
    console.error('Error getting access token:', error);
    return NextResponse.json({ error: 'Failed to get access token' }, { status: 500 });
  }
}
