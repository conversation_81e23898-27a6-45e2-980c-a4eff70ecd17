import { NextRequest, NextResponse } from 'next/server';
import { getPayloadHMR } from '@payloadcms/next/utilities';
import configPromise from '../../../../payload/payload.config';
import { syncAuth0User, validateAuth0Token } from '../../../../payload/hooks/auth0-sync';

// Handle Auth0 to Payload CMS authentication
export async function POST(req: NextRequest) {
  try {
    const payload = await getPayloadHMR({ config: configPromise });
    const body = await req.json();
    const { auth0Token, action } = body;

    if (!auth0Token) {
      return NextResponse.json(
        { error: 'Auth0 token is required' },
        { status: 400 }
      );
    }

    // Validate Auth0 token
    const auth0User = await validateAuth0Token(auth0Token);
    
    if (!auth0User) {
      return NextResponse.json(
        { error: 'Invalid Auth0 token' },
        { status: 401 }
      );
    }

    switch (action) {
      case 'login':
        // Sync user and create Payload session
        const user = await syncAuth0User(payload, auth0User);
        
        // Create Payload CMS session
        const loginResult = await payload.login({
          collection: 'users',
          data: {
            email: user.email,
            password: 'auth0-managed', // Placeholder since Auth0 manages authentication
          },
          req: {
            payload,
            user,
          } as any,
        });

        return NextResponse.json({
          success: true,
          user,
          token: loginResult.token,
          exp: loginResult.exp,
        });

      case 'sync':
        // Just sync user data without creating session
        const syncedUser = await syncAuth0User(payload, auth0User);
        
        return NextResponse.json({
          success: true,
          user: syncedUser,
        });

      case 'verify':
        // Just verify the token and return user info
        const existingUser = await payload.find({
          collection: 'users',
          where: {
            auth0Id: {
              equals: auth0User.sub,
            },
          },
          limit: 1,
        });

        if (existingUser.docs.length === 0) {
          return NextResponse.json(
            { error: 'User not found' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          user: existingUser.docs[0],
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Payload Auth API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle GET requests for user verification
export async function GET(req: NextRequest) {
  try {
    const payload = await getPayloadHMR({ config: configPromise });
    const authHeader = req.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization header required' },
        { status: 401 }
      );
    }

    const auth0Token = authHeader.substring(7);
    
    // Validate Auth0 token
    const auth0User = await validateAuth0Token(auth0Token);
    
    if (!auth0User) {
      return NextResponse.json(
        { error: 'Invalid Auth0 token' },
        { status: 401 }
      );
    }

    // Find user in Payload CMS
    const existingUser = await payload.find({
      collection: 'users',
      where: {
        auth0Id: {
          equals: auth0User.sub,
        },
      },
      limit: 1,
    });

    if (existingUser.docs.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      user: existingUser.docs[0],
    });
  } catch (error) {
    console.error('Payload Auth GET Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
