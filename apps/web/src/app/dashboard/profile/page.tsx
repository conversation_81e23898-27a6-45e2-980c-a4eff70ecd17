'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/auth-context';
import {
  getFactoryFromPayload,
  getCurrentFactoryId,
  isOnboardingComplete,
  updateFactoryInPayload
} from '../../../utils/payload-integration';
import type { Factory } from '../../../payload/payload-types';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../components/dashboard/dashboard-sidebar';
import { Button } from '../../../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Textarea } from '../../../components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select';
import { Badge } from '../../../components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../../../components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs';
import { Separator } from '../../../components/ui/separator';
import { 
  Building2, 
  Mail, 
  Phone, 
  Globe, 
  MapPin, 
  Calendar, 
  Users, 
  DollarSign,
  Shield,
  Settings,
  Camera,
  Save,
  Edit,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';

export default function FactoryProfilePage() {
  const { user, payloadUser } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Empty factory data template (for new factories)
  const emptyFactoryData = {
    name: '',
    description: '',
    email: '',
    phone: '',
    website: '',
    addressStreet: '',
    addressCity: '',
    addressState: '',
    addressPostalCode: '',
    addressCountry: '',
    businessLicense: '',
    taxId: '',
    establishedYear: new Date().getFullYear(),
    employeeCount: 1,
    annualRevenue: 0,
    currency: 'USD',
    timezone: 'UTC',
    language: 'en',
    verificationStatus: 'UNVERIFIED' as const,
    subscriptionTier: 'FREE' as const
  };

  // Demo factory data (only for development/demo purposes)
  const demoFactoryData = {
    name: 'Guangzhou Manufacturing Co., Ltd.',
    description: 'Leading manufacturer of electronic components and consumer goods with over 20 years of experience in international trade.',
    email: '<EMAIL>',
    phone: '+86 20 8888 9999',
    website: 'https://www.guangzhou-mfg.com',
    addressStreet: '123 Industrial Park Road',
    addressCity: 'Guangzhou',
    addressState: 'Guangdong',
    addressPostalCode: '510000',
    addressCountry: 'China',
    businessLicense: 'BL-2020-GZ-001234',
    taxId: 'TAX-GZ-567890',
    establishedYear: 2003,
    employeeCount: 250,
    annualRevenue: 15000000,
    currency: 'USD',
    timezone: 'Asia/Shanghai',
    language: 'en',
    verificationStatus: 'VERIFIED' as const,
    subscriptionTier: 'PREMIUM' as const
  };

  // Initialize with empty data first
  const [factoryData, setFactoryData] = useState<Partial<Factory>>(emptyFactoryData);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);

  // Load data on component mount from API
  useEffect(() => {
    const loadFactoryProfile = async () => {
      console.log('Loading factory profile data from API...');

      try {
        const onboardingComplete = isOnboardingComplete();
        const factoryId = getCurrentFactoryId();

        if (onboardingComplete && factoryId) {
          // Priority 1: Load from our API endpoint
          console.log('🔍 Loading factory from API:', factoryId);

          try {
            const response = await fetch('/api/factories', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
              },
            });

            if (response.ok) {
              const factories = await response.json();
              console.log('🔍 All factories from API:', factories);
              console.log('🔍 Looking for factory ID:', factoryId);

              const factory = factories.find((f: any) => f.id === factoryId) || factories[0];

              if (factory) {
                console.log('✅ Factory loaded from API:', factory);
                // Map API data to profile format (using actual database field names)
                const mappedFactory = {
                  // Basic profile fields (not nested)
                  name: factory.name || '',
                  description: factory.description || '',
                  email: factory.email || '',
                  phone: factory.phone || '',
                  website: factory.website || '',
                  logo: factory.logo || '',

                  // Address fields (individual fields, not nested)
                  addressStreet: factory.addressStreet || '',
                  addressCity: factory.addressCity || '',
                  addressState: factory.addressState || '',
                  addressPostalCode: factory.addressPostalCode || '',
                  addressCountry: factory.addressCountry || '',

                  // Business details
                  businessLicense: factory.businessLicense || '',
                  taxId: factory.taxId || '',
                  establishedYear: factory.establishedYear || new Date().getFullYear(),
                  employeeCount: factory.employeeCount || '',
                  annualRevenue: factory.annualRevenue || undefined,
                  currency: factory.currency || 'USD',
                  timezone: factory.timezone || 'UTC',
                  language: factory.language || 'en',
                  verificationStatus: factory.verificationStatus || 'PENDING',
                  subscriptionTier: factory.subscriptionTier || 'FREE',

                };

                console.log('🔄 Mapped factory data:', mappedFactory);
                console.log('🔍 Original factory keys:', Object.keys(factory));
                console.log('🔍 Factory name value:', factory.name);
                console.log('🔍 Factory description value:', factory.description);
                setFactoryData(mappedFactory);
              } else {
                console.warn('⚠️ Factory not found in API, using empty profile');
                setFactoryData(emptyFactoryData);
              }
            } else {
              console.warn('⚠️ Failed to load from API, using empty profile');
              setFactoryData(emptyFactoryData);
            }
          } catch (apiError) {
            console.error('❌ API error:', apiError);
            setFactoryData(emptyFactoryData);
          }
        } else {
          // Priority 2: Check if this is a demo/development environment
          const isDemoMode = process.env.NODE_ENV === 'development' &&
                            window.location.search.includes('demo=true');

          if (isDemoMode) {
            console.log('🎭 Demo mode: Loading demo factory data');
            setFactoryData(demoFactoryData);
          } else {
            // Priority 3: New factory - show empty profile
            console.log('🆕 New factory: Starting with empty profile');
            setFactoryData(emptyFactoryData);
          }
        }
      } catch (error) {
        console.error('❌ Error loading factory profile:', error);
        setFactoryData(emptyFactoryData);
      } finally {
        setIsLoadingProfile(false);
      }
    };

    loadFactoryProfile();
  }, []);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // TODO: Implement API call to save factory data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Mock delay
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving factory profile:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const getVerificationBadge = (status: string) => {
    switch (status) {
      case 'VERIFIED':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Verified</Badge>;
      case 'PENDING':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'REJECTED':
        return <Badge className="bg-red-100 text-red-800"><AlertCircle className="w-3 h-3 mr-1" />Rejected</Badge>;
      default:
        return <Badge variant="secondary">Unverified</Badge>;
    }
  };

  const getSubscriptionBadge = (tier: string) => {
    const colors = {
      FREE: 'bg-gray-100 text-gray-800',
      BASIC: 'bg-blue-100 text-blue-800',
      STANDARD: 'bg-purple-100 text-purple-800',
      PREMIUM: 'bg-orange-100 text-orange-800',
      ENTERPRISE: 'bg-red-100 text-red-800'
    };
    return <Badge className={colors[tier as keyof typeof colors] || colors.FREE}>{tier}</Badge>;
  };

  // Show loading state while profile is being loaded
  if (isLoadingProfile) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading factory profile...</p>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  // Check if this is a new factory with empty profile
  const isEmptyProfile = !factoryData.name && !isOnboardingComplete();

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Factory Profile</h1>
                <p className="text-gray-600">
                  {isEmptyProfile
                    ? "Complete your factory setup to get started"
                    : "Manage your factory information and settings"
                  }
                </p>
              </div>
              <div className="flex items-center space-x-3">
                {isEditing ? (
                  <>
                    <Button variant="outline" onClick={() => setIsEditing(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleSave} disabled={isSaving}>
                      <Save className="w-4 h-4 mr-2" />
                      {isSaving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </>
                ) : (
                  <Button onClick={() => setIsEditing(true)}>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Profile
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            <div className="max-w-4xl mx-auto space-y-6">

              {/* Empty State for New Factories */}
              {isEmptyProfile && (
                <Card className="border-dashed border-2 border-gray-300">
                  <CardContent className="p-12 text-center">
                    <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      Welcome to FC-CHINA!
                    </h3>
                    <p className="text-gray-600 mb-6 max-w-md mx-auto">
                      Complete your factory setup wizard to populate your profile with your business information,
                      capabilities, and team details.
                    </p>
                    <Button
                      onClick={() => window.location.href = '/onboarding'}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      Start Factory Setup Wizard
                    </Button>
                  </CardContent>
                </Card>
              )}

              {/* Profile Header Card - Only show if profile has data */}
              {!isEmptyProfile && (
                <Card>
                <CardContent className="p-6">
                  <div className="flex items-start space-x-6">
                    <div className="relative">
                      <Avatar className="h-24 w-24">
                        <AvatarImage src="/factory-logo.png" alt={factoryData.name} />
                        <AvatarFallback className="text-2xl">
                          <Building2 className="w-12 h-12" />
                        </AvatarFallback>
                      </Avatar>
                      {isEditing && (
                        <Button size="sm" className="absolute -bottom-2 -right-2 rounded-full p-2">
                          <Camera className="w-3 h-3" />
                        </Button>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h2 className="text-2xl font-bold text-gray-900">{factoryData.name}</h2>
                        {getVerificationBadge(factoryData.verificationStatus || 'PENDING')}
                        {getSubscriptionBadge(factoryData.subscriptionTier || 'FREE')}
                      </div>
                      <p className="text-gray-600 mb-4">{factoryData.description}</p>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center text-gray-500">
                          <Calendar className="w-4 h-4 mr-2" />
                          Est. {factoryData.establishedYear}
                        </div>
                        <div className="flex items-center text-gray-500">
                          <Users className="w-4 h-4 mr-2" />
                          {factoryData.employeeCount} employees
                        </div>
                        <div className="flex items-center text-gray-500">
                          <DollarSign className="w-4 h-4 mr-2" />
                          ${((factoryData.annualRevenue || 0) / 1000000).toFixed(1)}M revenue
                        </div>
                        <div className="flex items-center text-gray-500">
                          <MapPin className="w-4 h-4 mr-2" />
                          {factoryData.addressCity}, {factoryData.addressCountry}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              )}

              {/* Tabs - Only show if profile has data */}
              {!isEmptyProfile && (
                <Tabs defaultValue="general" className="space-y-6">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="general">General Info</TabsTrigger>
                  <TabsTrigger value="contact">Contact & Address</TabsTrigger>
                  <TabsTrigger value="business">Business Details</TabsTrigger>
                  <TabsTrigger value="settings">Settings</TabsTrigger>
                </TabsList>

                {/* General Information Tab */}
                <TabsContent value="general" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Basic Information</CardTitle>
                      <CardDescription>
                        Update your factory's basic information and description
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="factoryName">Factory Name</Label>
                          <Input
                            id="factoryName"
                            value={factoryData.name}
                            onChange={(e) => setFactoryData({...factoryData, name: e.target.value})}
                            disabled={!isEditing}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="website">Website</Label>
                          <Input
                            id="website"
                            type="url"
                            value={factoryData.website}
                            onChange={(e) => setFactoryData({...factoryData, website: e.target.value})}
                            disabled={!isEditing}
                            placeholder="https://www.example.com"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="description">Description</Label>
                        <Textarea
                          id="description"
                          value={factoryData.description}
                          onChange={(e) => setFactoryData({...factoryData, description: e.target.value})}
                          disabled={!isEditing}
                          rows={4}
                          placeholder="Describe your factory's capabilities and specialties..."
                        />
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Contact & Address Tab */}
                <TabsContent value="contact" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Contact Information</CardTitle>
                      <CardDescription>
                        Manage your factory's contact details and address
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="email">Email Address</Label>
                          <Input
                            id="email"
                            type="email"
                            value={factoryData.email}
                            onChange={(e) => setFactoryData({...factoryData, email: e.target.value})}
                            disabled={!isEditing}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="phone">Phone Number</Label>
                          <Input
                            id="phone"
                            value={factoryData.phone}
                            onChange={(e) => setFactoryData({...factoryData, phone: e.target.value})}
                            disabled={!isEditing}
                          />
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-4">
                        <h4 className="font-medium">Factory Address</h4>
                        <div className="space-y-2">
                          <Label htmlFor="street">Street Address</Label>
                          <Input
                            id="street"
                            value={factoryData.addressStreet || ''}
                            onChange={(e) => setFactoryData({
                              ...factoryData,
                              addressStreet: e.target.value
                            })}
                            disabled={!isEditing}
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="city">City</Label>
                            <Input
                              id="city"
                              value={factoryData.addressCity || ''}
                              onChange={(e) => setFactoryData({
                                ...factoryData,
                                addressCity: e.target.value
                              })}
                              disabled={!isEditing}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="state">State/Province</Label>
                            <Input
                              id="state"
                              value={factoryData.addressState || ''}
                              onChange={(e) => setFactoryData({
                                ...factoryData,
                                addressState: e.target.value
                              })}
                              disabled={!isEditing}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="postalCode">Postal Code</Label>
                            <Input
                              id="postalCode"
                              value={factoryData.addressPostalCode || ''}
                              onChange={(e) => setFactoryData({
                                ...factoryData,
                                addressPostalCode: e.target.value
                              })}
                              disabled={!isEditing}
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="country">Country</Label>
                          <Select 
                            value={factoryData.addressCountry || ''}
                            onValueChange={(value) => setFactoryData({
                              ...factoryData,
                              addressCountry: value
                            })}
                            disabled={!isEditing}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="China">China</SelectItem>
                              <SelectItem value="United States">United States</SelectItem>
                              <SelectItem value="Germany">Germany</SelectItem>
                              <SelectItem value="Japan">Japan</SelectItem>
                              <SelectItem value="South Korea">South Korea</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Business Details Tab */}
                <TabsContent value="business" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Business Information</CardTitle>
                      <CardDescription>
                        Legal and business details for verification and compliance
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="businessLicense">Business License</Label>
                          <Input
                            id="businessLicense"
                            value={factoryData.businessLicense}
                            onChange={(e) => setFactoryData({...factoryData, businessLicense: e.target.value})}
                            disabled={!isEditing}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="taxId">Tax ID</Label>
                          <Input
                            id="taxId"
                            value={factoryData.taxId}
                            onChange={(e) => setFactoryData({...factoryData, taxId: e.target.value})}
                            disabled={!isEditing}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="establishedYear">Established Year</Label>
                          <Input
                            id="establishedYear"
                            type="number"
                            min="1800"
                            max={new Date().getFullYear()}
                            value={factoryData.establishedYear}
                            onChange={(e) => setFactoryData({...factoryData, establishedYear: parseInt(e.target.value)})}
                            disabled={!isEditing}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="employeeCount">Employee Count</Label>
                          <Input
                            id="employeeCount"
                            type="number"
                            min="1"
                            value={factoryData.employeeCount}
                            onChange={(e) => setFactoryData({...factoryData, employeeCount: parseInt(e.target.value)})}
                            disabled={!isEditing}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="annualRevenue">Annual Revenue (USD)</Label>
                          <Input
                            id="annualRevenue"
                            type="number"
                            min="0"
                            value={factoryData.annualRevenue}
                            onChange={(e) => setFactoryData({...factoryData, annualRevenue: parseInt(e.target.value)})}
                            disabled={!isEditing}
                          />
                        </div>
                      </div>

                      <Separator />

                      <div className="space-y-4">
                        <h4 className="font-medium">Verification Status</h4>
                        <div className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <Shield className="w-5 h-5 text-blue-600" />
                            <div>
                              <p className="font-medium">Factory Verification</p>
                              <p className="text-sm text-gray-600">Your factory verification status</p>
                            </div>
                          </div>
                          {getVerificationBadge(factoryData.verificationStatus || 'PENDING')}
                        </div>

                        {factoryData.verificationStatus === 'VERIFIED' && (
                          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div className="flex items-center">
                              <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                              <p className="text-green-800 font-medium">Factory Verified</p>
                            </div>
                            <p className="text-green-700 text-sm mt-1">
                              Your factory has been successfully verified. You have access to all premium features.
                            </p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Settings Tab */}
                <TabsContent value="settings" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Platform Settings</CardTitle>
                      <CardDescription>
                        Configure your platform preferences and regional settings
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="currency">Default Currency</Label>
                          <Select
                            value={factoryData.currency}
                            onValueChange={(value) => setFactoryData({...factoryData, currency: value})}
                            disabled={!isEditing}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="USD">US Dollar (USD)</SelectItem>
                              <SelectItem value="CNY">Chinese Yuan (CNY)</SelectItem>
                              <SelectItem value="EUR">Euro (EUR)</SelectItem>
                              <SelectItem value="GBP">British Pound (GBP)</SelectItem>
                              <SelectItem value="JPY">Japanese Yen (JPY)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="timezone">Timezone</Label>
                          <Select
                            value={factoryData.timezone}
                            onValueChange={(value) => setFactoryData({...factoryData, timezone: value})}
                            disabled={!isEditing}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Asia/Shanghai">Asia/Shanghai (UTC+8)</SelectItem>
                              <SelectItem value="America/New_York">America/New_York (UTC-5)</SelectItem>
                              <SelectItem value="Europe/London">Europe/London (UTC+0)</SelectItem>
                              <SelectItem value="Asia/Tokyo">Asia/Tokyo (UTC+9)</SelectItem>
                              <SelectItem value="UTC">UTC (UTC+0)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="language">Default Language</Label>
                        <Select
                          value={factoryData.language}
                          onValueChange={(value) => setFactoryData({...factoryData, language: value})}
                          disabled={!isEditing}
                        >
                          <SelectTrigger className="w-full md:w-1/2">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="en">English</SelectItem>
                            <SelectItem value="zh-CN">简体中文</SelectItem>
                            <SelectItem value="zh-TW">繁體中文</SelectItem>
                            <SelectItem value="es">Español</SelectItem>
                            <SelectItem value="fr">Français</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <Separator />

                      <div className="space-y-4">
                        <h4 className="font-medium">Subscription</h4>
                        <div className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <Settings className="w-5 h-5 text-purple-600" />
                            <div>
                              <p className="font-medium">Current Plan</p>
                              <p className="text-sm text-gray-600">Your current subscription tier</p>
                            </div>
                          </div>
                          {getSubscriptionBadge(factoryData.subscriptionTier || 'FREE')}
                        </div>

                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-blue-900">Premium Features Enabled</p>
                              <p className="text-blue-700 text-sm">
                                Advanced analytics, priority support, and unlimited products
                              </p>
                            </div>
                            <Button variant="outline" size="sm">
                              Manage Plan
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
              )}
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
