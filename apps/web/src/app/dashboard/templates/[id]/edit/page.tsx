'use client';

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '../../../../../contexts/auth-context';
import { ProtectedRoute } from '../../../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../../../components/dashboard/dashboard-sidebar';
import { Button } from '../../../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../../../components/ui/card';
import { Input } from '../../../../../components/ui/input';
import { Textarea } from '../../../../../components/ui/textarea';
import { Label } from '../../../../../components/ui/label';
import { Badge } from '../../../../../components/ui/badge';
import { Switch } from '../../../../../components/ui/switch';
import {
  ArrowLeft,
  Save,
  Plus,
  Trash2,
  Package,
  Search,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';
import { trpc } from '../../../../../lib/trpc';
import { TemplateType } from '@fc-china/shared-types';

interface TemplateItem {
  id?: string;
  productId: string;
  variantId?: string;
  quantity: number;
  unitPrice?: number;
  specifications?: Record<string, any>;
  notes?: string;
  sortOrder: number;
  // For display purposes
  product?: {
    id: string;
    name: string;
    sku: string;
    basePrice: number;
    mainImage?: string;
  };
  variant?: {
    id: string;
    name: string;
    price: number;
  };
}

interface TemplateFormData {
  name: string;
  description: string;
  templateType: TemplateType;
  isActive: boolean;
  isPublic: boolean;
  defaultCustomerName: string;
  defaultCustomerEmail: string;
  defaultCustomerPhone: string;
  defaultCustomerCompany: string;
  defaultShippingStreet: string;
  defaultShippingCity: string;
  defaultShippingState: string;
  defaultShippingPostalCode: string;
  defaultShippingCountry: string;
  defaultCurrency: string;
  defaultNotes: string;
  items: TemplateItem[];
}

export default function EditTemplatePage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const templateId = params.id as string;
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showProductSearch, setShowProductSearch] = useState(false);
  const [productSearchQuery, setProductSearchQuery] = useState('');
  const [formData, setFormData] = useState<TemplateFormData | null>(null);

  // Fetch template data
  const { data: template, isLoading, error } = trpc.orderTemplates.getById.useQuery({
    id: templateId,
  });

  // Fetch products for selection
  const { data: productsData } = trpc.products.getAll.useQuery({
    page: 1,
    limit: 100,
    search: productSearchQuery || undefined,
  });

  const products = productsData?.data || [];

  const updateTemplate = trpc.orderTemplates.update.useMutation({
    onSuccess: (data) => {
      router.push(`/dashboard/templates/${data.id}`);
    },
    onError: (error) => {
      console.error('Failed to update template:', error);
      alert('Failed to update template. Please try again.');
    },
  });

  // Pre-populate form data when template loads
  useEffect(() => {
    if (template && !formData) {
      setFormData({
        name: template.name,
        description: template.description || '',
        templateType: template.templateType,
        isActive: template.isActive,
        isPublic: template.isPublic,
        defaultCustomerName: template.defaultCustomerName || '',
        defaultCustomerEmail: template.defaultCustomerEmail || '',
        defaultCustomerPhone: template.defaultCustomerPhone || '',
        defaultCustomerCompany: template.defaultCustomerCompany || '',
        defaultShippingStreet: template.defaultShippingStreet || '',
        defaultShippingCity: template.defaultShippingCity || '',
        defaultShippingState: template.defaultShippingState || '',
        defaultShippingPostalCode: template.defaultShippingPostalCode || '',
        defaultShippingCountry: template.defaultShippingCountry || '',
        defaultCurrency: template.defaultCurrency,
        defaultNotes: template.defaultNotes || '',
        items: template.items.map((item, index) => ({
          id: item.id,
          productId: item.productId,
          variantId: item.variantId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          specifications: item.specifications,
          notes: item.notes,
          sortOrder: item.sortOrder || index,
          product: item.product,
          variant: item.variant,
        })),
      });
    }
  }, [template, formData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData || formData.items.length === 0) {
      alert('Please add at least one item to the template.');
      return;
    }

    setIsSubmitting(true);
    try {
      await updateTemplate.mutateAsync({
        id: templateId,
        name: formData.name,
        description: formData.description || undefined,
        templateType: formData.templateType,
        isActive: formData.isActive,
        isPublic: formData.isPublic,
        defaultCustomerName: formData.defaultCustomerName || undefined,
        defaultCustomerEmail: formData.defaultCustomerEmail || undefined,
        defaultCustomerPhone: formData.defaultCustomerPhone || undefined,
        defaultCustomerCompany: formData.defaultCustomerCompany || undefined,
        defaultShippingStreet: formData.defaultShippingStreet || undefined,
        defaultShippingCity: formData.defaultShippingCity || undefined,
        defaultShippingState: formData.defaultShippingState || undefined,
        defaultShippingPostalCode: formData.defaultShippingPostalCode || undefined,
        defaultShippingCountry: formData.defaultShippingCountry || undefined,
        defaultCurrency: formData.defaultCurrency as any,
        defaultNotes: formData.defaultNotes || undefined,
        items: formData.items.map(item => ({
          productId: item.productId,
          variantId: item.variantId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          specifications: item.specifications,
          notes: item.notes,
          sortOrder: item.sortOrder,
        })),
      });
    } catch (error) {
      console.error('Error updating template:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const addProduct = (product: any) => {
    if (!formData) return;
    
    const newItem: TemplateItem = {
      productId: product.id,
      quantity: 1,
      unitPrice: product.basePrice,
      sortOrder: formData.items.length,
      product: {
        id: product.id,
        name: product.name,
        sku: product.sku,
        basePrice: product.basePrice,
        mainImage: product.images?.find((img: any) => img.isMain)?.url,
      },
    };

    setFormData(prev => prev ? ({
      ...prev,
      items: [...prev.items, newItem],
    }) : null);
    setShowProductSearch(false);
    setProductSearchQuery('');
  };

  const removeItem = (index: number) => {
    if (!formData) return;
    
    setFormData(prev => prev ? ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }) : null);
  };

  const updateItem = (index: number, updates: Partial<TemplateItem>) => {
    if (!formData) return;
    
    setFormData(prev => prev ? ({
      ...prev,
      items: prev.items.map((item, i) => 
        i === index ? { ...item, ...updates } : item
      ),
    }) : null);
  };

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
            <div className="flex-1 overflow-auto p-6">
              <div className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-64 bg-gray-200 rounded-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (error || !template) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
            <div className="flex-1 overflow-auto p-6">
              <Card>
                <CardContent className="p-12 text-center">
                  <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Template not found
                  </h3>
                  <p className="text-gray-600 mb-6">
                    The template you're trying to edit doesn't exist or has been deleted.
                  </p>
                  <Link href="/dashboard/templates">
                    <Button>
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Back to Templates
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (!formData) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
            <div className="flex-1 overflow-auto p-6">
              <div className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-64 bg-gray-200 rounded-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href={`/dashboard/templates/${templateId}`}>
                  <Button variant="outline" size="sm">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back
                  </Button>
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Edit Template</h1>
                  <p className="text-gray-600">Modify "{template.name}"</p>
                </div>
              </div>
              
              <Button 
                onClick={handleSubmit}
                disabled={isSubmitting || !formData.name || formData.items.length === 0}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Save className="w-4 h-4 mr-2" />
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <BasicInformationSection formData={formData} setFormData={setFormData} />

              {/* Template Items */}
              <TemplateItemsSection 
                items={formData.items}
                onAddProduct={() => setShowProductSearch(true)}
                onRemoveItem={removeItem}
                onUpdateItem={updateItem}
              />

              {/* Default Settings */}
              <DefaultSettingsSection formData={formData} setFormData={setFormData} />

              {/* Product Search Modal */}
              {showProductSearch && (
                <ProductSearchModal
                  products={products}
                  searchQuery={productSearchQuery}
                  onSearchChange={setProductSearchQuery}
                  onSelectProduct={addProduct}
                  onClose={() => setShowProductSearch(false)}
                />
              )}
            </form>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}

function BasicInformationSection({ formData, setFormData }: any) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Basic Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">Template Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData((prev: any) => ({ ...prev, name: e.target.value }))}
              placeholder="Enter template name"
              required
            />
          </div>
          <div>
            <Label htmlFor="templateType">Template Type</Label>
            <select
              id="templateType"
              value={formData.templateType}
              onChange={(e) => setFormData((prev: any) => ({ ...prev, templateType: e.target.value as TemplateType }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="STANDARD">Standard</option>
              <option value="RECURRING">Recurring</option>
              <option value="CUSTOMER">Customer</option>
              <option value="SEASONAL">Seasonal</option>
            </select>
          </div>
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, description: e.target.value }))}
            placeholder="Enter template description"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => setFormData((prev: any) => ({ ...prev, isActive: checked }))}
            />
            <Label htmlFor="isActive">Template is active</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isPublic"
              checked={formData.isPublic}
              onCheckedChange={(checked) => setFormData((prev: any) => ({ ...prev, isPublic: checked }))}
            />
            <Label htmlFor="isPublic">Make template public</Label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function TemplateItemsSection({ items, onAddProduct, onRemoveItem, onUpdateItem }: any) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Package className="w-5 h-5 mr-2" />
            Template Items ({items.length})
          </CardTitle>
          <Button type="button" onClick={onAddProduct} variant="outline">
            <Plus className="w-4 h-4 mr-2" />
            Add Product
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {items.length === 0 ? (
          <div className="text-center py-8">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">No items added yet</p>
            <Button type="button" onClick={onAddProduct}>
              <Plus className="w-4 h-4 mr-2" />
              Add Your First Product
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {items.map((item: TemplateItem, index: number) => (
              <div key={item.id || index} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  {item.product?.mainImage ? (
                    <img
                      src={item.product.mainImage}
                      alt={item.product.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <Package className="w-6 h-6 text-gray-400" />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900">{item.product?.name}</h4>
                  <p className="text-sm text-gray-600">SKU: {item.product?.sku}</p>
                  <p className="text-sm text-gray-600">
                    Base Price: ${item.product?.basePrice.toFixed(2)}
                  </p>
                </div>

                <div className="flex items-center space-x-2">
                  <div>
                    <Label className="text-xs">Quantity</Label>
                    <Input
                      type="number"
                      min="1"
                      value={item.quantity}
                      onChange={(e) => onUpdateItem(index, { quantity: parseInt(e.target.value) || 1 })}
                      className="w-20"
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Unit Price</Label>
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={item.unitPrice || ''}
                      onChange={(e) => onUpdateItem(index, { unitPrice: parseFloat(e.target.value) || undefined })}
                      placeholder={item.product?.basePrice.toFixed(2)}
                      className="w-24"
                    />
                  </div>
                  <div className="text-right">
                    <Label className="text-xs">Total</Label>
                    <p className="text-sm font-medium">
                      ${((item.unitPrice || item.product?.basePrice || 0) * item.quantity).toFixed(2)}
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => onRemoveItem(index)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function DefaultSettingsSection({ formData, setFormData }: any) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Default Settings</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Customer Defaults */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 mb-4">Default Customer Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="defaultCustomerName">Customer Name</Label>
              <Input
                id="defaultCustomerName"
                value={formData.defaultCustomerName}
                onChange={(e) => setFormData((prev: any) => ({ ...prev, defaultCustomerName: e.target.value }))}
                placeholder="Enter default customer name"
              />
            </div>
            <div>
              <Label htmlFor="defaultCustomerEmail">Email</Label>
              <Input
                id="defaultCustomerEmail"
                type="email"
                value={formData.defaultCustomerEmail}
                onChange={(e) => setFormData((prev: any) => ({ ...prev, defaultCustomerEmail: e.target.value }))}
                placeholder="Enter default email"
              />
            </div>
            <div>
              <Label htmlFor="defaultCustomerPhone">Phone</Label>
              <Input
                id="defaultCustomerPhone"
                value={formData.defaultCustomerPhone}
                onChange={(e) => setFormData((prev: any) => ({ ...prev, defaultCustomerPhone: e.target.value }))}
                placeholder="Enter default phone"
              />
            </div>
            <div>
              <Label htmlFor="defaultCustomerCompany">Company</Label>
              <Input
                id="defaultCustomerCompany"
                value={formData.defaultCustomerCompany}
                onChange={(e) => setFormData((prev: any) => ({ ...prev, defaultCustomerCompany: e.target.value }))}
                placeholder="Enter default company"
              />
            </div>
          </div>
        </div>

        {/* Shipping Defaults */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 mb-4">Default Shipping Address</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="defaultShippingStreet">Street Address</Label>
              <Input
                id="defaultShippingStreet"
                value={formData.defaultShippingStreet}
                onChange={(e) => setFormData((prev: any) => ({ ...prev, defaultShippingStreet: e.target.value }))}
                placeholder="Enter default street address"
              />
            </div>
            <div>
              <Label htmlFor="defaultShippingCity">City</Label>
              <Input
                id="defaultShippingCity"
                value={formData.defaultShippingCity}
                onChange={(e) => setFormData((prev: any) => ({ ...prev, defaultShippingCity: e.target.value }))}
                placeholder="Enter default city"
              />
            </div>
            <div>
              <Label htmlFor="defaultShippingState">State/Province</Label>
              <Input
                id="defaultShippingState"
                value={formData.defaultShippingState}
                onChange={(e) => setFormData((prev: any) => ({ ...prev, defaultShippingState: e.target.value }))}
                placeholder="Enter default state"
              />
            </div>
            <div>
              <Label htmlFor="defaultShippingPostalCode">Postal Code</Label>
              <Input
                id="defaultShippingPostalCode"
                value={formData.defaultShippingPostalCode}
                onChange={(e) => setFormData((prev: any) => ({ ...prev, defaultShippingPostalCode: e.target.value }))}
                placeholder="Enter default postal code"
              />
            </div>
            <div>
              <Label htmlFor="defaultShippingCountry">Country</Label>
              <Input
                id="defaultShippingCountry"
                value={formData.defaultShippingCountry}
                onChange={(e) => setFormData((prev: any) => ({ ...prev, defaultShippingCountry: e.target.value }))}
                placeholder="Enter default country"
              />
            </div>
          </div>
        </div>

        {/* Template Settings */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 mb-4">Template Settings</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="defaultCurrency">Default Currency</Label>
              <select
                id="defaultCurrency"
                value={formData.defaultCurrency}
                onChange={(e) => setFormData((prev: any) => ({ ...prev, defaultCurrency: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="USD">USD - US Dollar</option>
                <option value="EUR">EUR - Euro</option>
                <option value="GBP">GBP - British Pound</option>
                <option value="CNY">CNY - Chinese Yuan</option>
                <option value="JPY">JPY - Japanese Yen</option>
                <option value="KRW">KRW - Korean Won</option>
              </select>
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="defaultNotes">Default Notes</Label>
              <Textarea
                id="defaultNotes"
                value={formData.defaultNotes}
                onChange={(e) => setFormData((prev: any) => ({ ...prev, defaultNotes: e.target.value }))}
                placeholder="Enter default notes for orders created from this template"
                rows={3}
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function ProductSearchModal({ products, searchQuery, onSearchChange, onSelectProduct, onClose }: any) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Add Product</h3>
          <Button variant="outline" size="sm" onClick={onClose}>
            ×
          </Button>
        </div>

        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search products..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="max-h-96 overflow-y-auto space-y-2">
          {products.length === 0 ? (
            <div className="text-center py-8">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No products found</p>
            </div>
          ) : (
            products.map((product: any) => (
              <div
                key={product.id}
                className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                onClick={() => onSelectProduct(product)}
              >
                <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  {product.images?.find((img: any) => img.isMain) ? (
                    <img
                      src={product.images.find((img: any) => img.isMain).url}
                      alt={product.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <Package className="w-5 h-5 text-gray-400" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 truncate">{product.name}</h4>
                  <p className="text-sm text-gray-600">SKU: {product.sku}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    ${product.basePrice.toFixed(2)}
                  </p>
                  <Badge className={
                    product.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }>
                    {product.status}
                  </Badge>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
