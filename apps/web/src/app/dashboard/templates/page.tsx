'use client';

import { useState } from 'react';
import { useAuth } from '../../../contexts/auth-context';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../components/dashboard/dashboard-sidebar';
import { But<PERSON> } from '../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Input } from '../../../components/ui/input';
import { Badge } from '../../../components/ui/badge';
import {
  Plus,
  Search,
  Filter,
  Grid3X3,
  List,
  Copy,
  Eye,
  Edit,
  Trash2,
  Star,
  DollarSign,
  Calendar,
  Users,
  Package,
  ShoppingCart
} from 'lucide-react';
import Link from 'next/link';
import { trpc } from '../../../lib/trpc';
import { TemplateType } from '@fc-china/shared-types';

interface OrderTemplate {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
  isPublic: boolean;
  templateType: TemplateType;
  defaultCurrency: string;
  estimatedTotalAmount: number | null;
  items: Array<{
    id: string;
    quantity: number;
    unitPrice?: number;
    product: {
      id: string;
      name: string;
      sku: string;
      basePrice: number;
      mainImage?: string;
    };
    variant?: {
      id: string;
      name: string;
      price: number;
    };
  }>;
  orders?: Array<{
    id: string;
    orderNumber: string;
    status: string;
    totalAmount: number;
    createdAt: string;
  }>;
  recurringOrders?: Array<{
    id: string;
    name: string;
    isActive: boolean;
    frequency: string;
    nextRunDate: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

const templateTypeColors = {
  STANDARD: 'bg-blue-100 text-blue-800',
  RECURRING: 'bg-green-100 text-green-800',
  CUSTOMER: 'bg-purple-100 text-purple-800',
  SEASONAL: 'bg-orange-100 text-orange-800',
};

const templateTypeLabels = {
  STANDARD: 'Standard',
  RECURRING: 'Recurring',
  CUSTOMER: 'Customer',
  SEASONAL: 'Seasonal',
};

const getStatusColor = (isActive: boolean) => {
  return isActive
    ? 'bg-green-100 text-green-800'
    : 'bg-gray-100 text-gray-800';
};

export default function TemplatesPage() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedType, setSelectedType] = useState<TemplateType | ''>('');
  const [showActiveOnly, setShowActiveOnly] = useState(true);

  // Fetch templates with tRPC
  const { data: templatesData, isLoading, refetch } = trpc.orderTemplates.getAll.useQuery({
    page: 1,
    limit: 50,
    templateType: selectedType || undefined,
    isActive: showActiveOnly ? true : undefined,
    search: searchQuery || undefined,
  });

  const templates = templatesData?.data || [];
  const pagination = templatesData?.pagination;

  // Filter templates based on search
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = !searchQuery || 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description?.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesSearch;
  });

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? 'bg-green-100 text-green-800' 
      : 'bg-gray-100 text-gray-800';
  };

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
            <div className="flex-1 overflow-auto p-6">
              <div className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="h-64 bg-gray-200 rounded-lg"></div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Order Templates</h1>
                <p className="text-gray-600">
                  Manage reusable order templates ({filteredTemplates.length} templates)
                </p>
              </div>
              <Link href="/dashboard/templates/create">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Template
                </Button>
              </Link>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Type Filter */}
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value as TemplateType | '')}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Types</option>
                <option value="STANDARD">Standard</option>
                <option value="RECURRING">Recurring</option>
                <option value="CUSTOMER">Customer</option>
                <option value="SEASONAL">Seasonal</option>
              </select>

              {/* Active Filter */}
              <Button
                variant={showActiveOnly ? "default" : "outline"}
                size="sm"
                onClick={() => setShowActiveOnly(!showActiveOnly)}
              >
                <Filter className="w-4 h-4 mr-2" />
                {showActiveOnly ? 'Active Only' : 'All Status'}
              </Button>

              {/* View Mode */}
              <div className="flex border border-gray-300 rounded-md">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            {filteredTemplates.length === 0 ? (
              <Card className="border-dashed border-2 border-gray-300">
                <CardContent className="p-12 text-center">
                  <Copy className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {templates.length === 0 ? 'No templates yet' : 'No templates found'}
                  </h3>
                  <p className="text-gray-600 mb-6 max-w-md mx-auto">
                    {templates.length === 0 
                      ? 'Create your first order template to streamline your order process.'
                      : 'Try adjusting your search or filter criteria.'
                    }
                  </p>
                  {templates.length === 0 && (
                    <Link href="/dashboard/templates/create">
                      <Button className="bg-blue-600 hover:bg-blue-700">
                        <Plus className="w-4 h-4 mr-2" />
                        Create Your First Template
                      </Button>
                    </Link>
                  )}
                </CardContent>
              </Card>
            ) : (
              <div className={viewMode === 'grid' 
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                : 'space-y-4'
              }>
                {filteredTemplates.map(template => (
                  <TemplateCard 
                    key={template.id} 
                    template={template} 
                    viewMode={viewMode}
                    onRefetch={refetch}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}

interface TemplateCardProps {
  template: OrderTemplate;
  viewMode: 'grid' | 'list';
  onRefetch: () => void;
}

function TemplateCard({ template, viewMode, onRefetch }: TemplateCardProps) {
  const deleteTemplate = trpc.orderTemplates.delete.useMutation({
    onSuccess: () => {
      onRefetch();
    },
  });

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete "${template.name}"?`)) {
      try {
        await deleteTemplate.mutateAsync({ id: template.id });
      } catch (error) {
        console.error('Failed to delete template:', error);
      }
    }
  };

  const totalItems = template.items.length;
  const totalOrders = template.orders?.length || 0;
  const activeRecurringOrders = template.recurringOrders?.filter(ro => ro.isActive).length || 0;

  if (viewMode === 'grid') {
    return (
      <Card className="hover:shadow-lg transition-shadow">
        <CardContent className="p-4">
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 truncate mb-1">
                {template.name}
              </h3>
              <div className="flex items-center space-x-2">
                <Badge className={templateTypeColors[template.templateType]}>
                  {templateTypeLabels[template.templateType]}
                </Badge>
                <Badge className={getStatusColor(template.isActive)}>
                  {template.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          </div>

          {/* Description */}
          {template.description && (
            <p className="text-sm text-gray-600 mb-3 line-clamp-2">
              {template.description}
            </p>
          )}

          {/* Stats */}
          <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="text-center p-2 bg-gray-50 rounded">
              <div className="text-lg font-semibold text-gray-900">{totalItems}</div>
              <div className="text-xs text-gray-600">Items</div>
            </div>
            <div className="text-center p-2 bg-gray-50 rounded">
              <div className="text-lg font-semibold text-gray-900">{totalOrders}</div>
              <div className="text-xs text-gray-600">Orders</div>
            </div>
          </div>

          {/* Estimated Total */}
          {template.estimatedTotalAmount && (
            <div className="flex items-center justify-center mb-4 p-2 bg-blue-50 rounded">
              <DollarSign className="w-4 h-4 text-blue-600 mr-1" />
              <span className="text-sm font-medium text-blue-900">
                ${template.estimatedTotalAmount.toFixed(2)} {template.defaultCurrency}
              </span>
            </div>
          )}

          {/* Recurring Orders */}
          {activeRecurringOrders > 0 && (
            <div className="flex items-center justify-center mb-4 p-2 bg-green-50 rounded">
              <Calendar className="w-4 h-4 text-green-600 mr-1" />
              <span className="text-sm font-medium text-green-900">
                {activeRecurringOrders} Active Recurring
              </span>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-2">
            <Link href={`/dashboard/templates/${template.id}`} className="flex-1">
              <Button variant="outline" size="sm" className="w-full">
                <Eye className="w-4 h-4 mr-1" />
                View
              </Button>
            </Link>
            <Link href={`/dashboard/templates/${template.id}/edit`} className="flex-1">
              <Button variant="outline" size="sm" className="w-full">
                <Edit className="w-4 h-4 mr-1" />
                Edit
              </Button>
            </Link>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDelete}
              disabled={deleteTemplate.isPending}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // List view
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* Left side - Template info */}
          <div className="flex items-center space-x-4 flex-1 min-w-0">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <Copy className="w-6 h-6 text-blue-600" />
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="font-semibold text-gray-900 truncate">
                  {template.name}
                </h3>
                <Badge className={templateTypeColors[template.templateType]}>
                  {templateTypeLabels[template.templateType]}
                </Badge>
                <Badge className={getStatusColor(template.isActive)}>
                  {template.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>

              {template.description && (
                <p className="text-sm text-gray-600 truncate mb-1">
                  {template.description}
                </p>
              )}

              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span className="flex items-center">
                  <Package className="w-4 h-4 mr-1" />
                  {totalItems} items
                </span>
                <span className="flex items-center">
                  <ShoppingCart className="w-4 h-4 mr-1" />
                  {totalOrders} orders
                </span>
                {activeRecurringOrders > 0 && (
                  <span className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {activeRecurringOrders} recurring
                  </span>
                )}
                {template.estimatedTotalAmount && (
                  <span className="flex items-center font-medium text-gray-900">
                    <DollarSign className="w-4 h-4 mr-1" />
                    ${template.estimatedTotalAmount.toFixed(2)} {template.defaultCurrency}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Right side - Actions */}
          <div className="flex items-center space-x-2 flex-shrink-0">
            <Link href={`/dashboard/templates/${template.id}`}>
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4 mr-1" />
                View
              </Button>
            </Link>
            <Link href={`/dashboard/templates/${template.id}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="w-4 h-4 mr-1" />
                Edit
              </Button>
            </Link>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDelete}
              disabled={deleteTemplate.isPending}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
