'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/auth-context';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../components/dashboard/dashboard-sidebar';
import { Button } from '../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Input } from '../../../components/ui/input';
import { Badge } from '../../../components/ui/badge';
import {
  Plus,
  Search,
  Filter,
  Grid3X3,
  List,
  Package,
  Eye,
  Edit,
  Trash2,
  Star,
  DollarSign
} from 'lucide-react';
import Link from 'next/link';

// Client component for action buttons
function ProductActionButtons({ product, onDelete }: { product: any, onDelete: (productId: string) => void }) {
  const handleDelete = async () => {
    if (confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
      try {
        console.log('🔄 Deleting product:', product.id);
        await onDelete(product.id);
      } catch (error) {
        console.error('❌ Error deleting product:', error);
        alert('Failed to delete product. Please try again.');
      }
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <Link href={`/dashboard/products/${product.id}`}>
        <Button size="sm" variant="outline">
          <Eye className="w-4 h-4" />
        </Button>
      </Link>
      <Link href={`/dashboard/products/${product.id}/edit`}>
        <Button size="sm" variant="outline">
          <Edit className="w-4 h-4" />
        </Button>
      </Link>
      <Button
        size="sm"
        variant="outline"
        className="text-red-600 hover:text-red-700"
        onClick={handleDelete}
      >
        <Trash2 className="w-4 h-4" />
      </Button>
    </div>
  );
}

// Client component for view mode toggle
function ViewModeToggle({ viewMode, setViewMode }: { viewMode: 'grid' | 'list', setViewMode: React.Dispatch<React.SetStateAction<'grid' | 'list'>> }) {
  return (
    <div className="flex border border-gray-300 rounded-md">
      <Button
        variant={viewMode === 'grid' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => setViewMode('grid')}
        className="rounded-r-none"
      >
        <Grid3X3 className="w-4 h-4" />
      </Button>
      <Button
        variant={viewMode === 'list' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => setViewMode('list')}
        className="rounded-l-none"
      >
        <List className="w-4 h-4" />
      </Button>
    </div>
  );
}

interface Product {
  id: string;
  name: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  basePrice: number;
  currency: string;
  minOrderQty: number;
  sku?: string;
  status: 'DRAFT' | 'ACTIVE' | 'INACTIVE' | 'OUT_OF_STOCK' | 'DISCONTINUED';
  isFeatured: boolean;
  stockQuantity: number;
  stockStatus: string;
  category: {
    id: string;
    name: string;
  };
  images: Array<{
    id: string;
    url: string;
    isMain: boolean;
  }>;
  createdAt: string;
  updatedAt: string;
}

export default function ProductsPage() {
  const { user } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [categories, setCategories] = useState<Array<{ id: string; name: string }>>([]);

  // Delete product function with retry logic
  const handleDeleteProduct = async (productId: string) => {
    const factoryId = user?.factoryId;
    if (!factoryId) {
      throw new Error('No factory ID found');
    }

    console.log('🔄 Attempting to delete product:', { productId, factoryId });

    // Single delete attempt (no retry for delete operations)
    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ factoryId }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Product deleted successfully:', result);

        // Remove product from local state immediately
        setProducts(prev => prev.filter(p => p.id !== productId));

        // Reload data to ensure consistency - trigger a re-fetch
        window.location.reload();
        return;
      } else {
        const errorText = await response.text();
        console.error(`❌ Delete API Error:`, response.status, errorText);
        throw new Error(`Failed to delete product: ${errorText}`);
      }
    } catch (error) {
      console.error(`❌ Delete error:`, error);
      throw error;
    }
  };

  // Load products and categories
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Get factory ID from user context
        const factoryId = user?.factoryId;
        if (!factoryId) {
          console.error('No factory ID found for user');
          return;
        }

        console.log('🔍 Using factoryId:', factoryId, 'from user:', user?.factoryId);

        // Load products with retry logic
        let productsLoaded = false;
        for (let attempt = 1; attempt <= 3; attempt++) {
          try {
            const productsResponse = await fetch(`/api/products?factoryId=${factoryId}`);
            if (productsResponse.ok) {
              const productsData = await productsResponse.json();
              console.log('🔍 Products API Response:', productsData);
              setProducts(productsData.products || []);
              productsLoaded = true;
              break;
            } else {
              const errorText = await productsResponse.text();
              console.error(`❌ Products API Error (attempt ${attempt}/3):`, productsResponse.status, errorText);

              if (productsResponse.status === 500 && attempt < 3) {
                console.log(`🔄 Retrying products load in ${attempt}s...`);
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
                continue;
              }
            }
          } catch (error) {
            console.error(`❌ Products fetch error (attempt ${attempt}/3):`, error);
            if (attempt < 3) {
              await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
              continue;
            }
          }
        }

        if (!productsLoaded) {
          console.error('❌ Failed to load products after all retries');
          setProducts([]);
        }

        // Load categories with retry logic
        let categoriesLoaded = false;
        for (let attempt = 1; attempt <= 3; attempt++) {
          try {
            const categoriesResponse = await fetch(`/api/categories?factoryId=${factoryId}`);
            if (categoriesResponse.ok) {
              const categoriesData = await categoriesResponse.json();
              console.log('🔍 Categories API Response:', categoriesData);
              setCategories(categoriesData.categories || []);
              categoriesLoaded = true;
              break;
            } else {
              const errorText = await categoriesResponse.text();
              console.error(`❌ Categories API Error (attempt ${attempt}/3):`, categoriesResponse.status, errorText);

              if (categoriesResponse.status === 500 && attempt < 3) {
                console.log(`🔄 Retrying categories load in ${attempt}s...`);
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
                continue;
              }
            }
          } catch (error) {
            console.error(`❌ Categories fetch error (attempt ${attempt}/3):`, error);
            if (attempt < 3) {
              await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
              continue;
            }
          }
        }

        if (!categoriesLoaded) {
          console.error('❌ Failed to load categories after all retries');
          setCategories([]);
        }

      } catch (error) {
        console.error('Error loading products:', error);
      } finally {
        setLoading(false);
      }
    };

    if (user?.factoryId) {
      loadData();
    }
  }, [user?.factoryId]);

  // Debug logging
  console.log('🔍 Products State:', {
    products,
    productsCount: products.length,
    loading,
    userFactoryId: user?.factoryId
  });

  // Filter products based on search and category
  const filteredProducts = products.filter(product => {
    const matchesSearch = !searchQuery || 
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.sku?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = !selectedCategory || product.category.id === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800';
      case 'DRAFT': return 'bg-gray-100 text-gray-800';
      case 'INACTIVE': return 'bg-red-100 text-red-800';
      case 'OUT_OF_STOCK': return 'bg-orange-100 text-orange-800';
      case 'DISCONTINUED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case 'IN_STOCK': return 'bg-green-100 text-green-800';
      case 'LOW_STOCK': return 'bg-yellow-100 text-yellow-800';
      case 'OUT_OF_STOCK': return 'bg-red-100 text-red-800';
      case 'BACKORDER': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-4 animate-pulse" />
              <p className="text-gray-600">Loading products...</p>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Product Management</h1>
                <p className="text-gray-600">
                  Manage your factory's product catalog ({filteredProducts.length} products)
                </p>
              </div>
              <Link href="/dashboard/products/create">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Product
                </Button>
              </Link>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search products by name, description, or SKU..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Category Filter */}
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>

              {/* View Mode Toggle */}
              <ViewModeToggle viewMode={viewMode} setViewMode={setViewMode} />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            {filteredProducts.length === 0 ? (
              <Card className="border-dashed border-2 border-gray-300">
                <CardContent className="p-12 text-center">
                  <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {products.length === 0 ? 'No products yet' : 'No products found'}
                  </h3>
                  <p className="text-gray-600 mb-6 max-w-md mx-auto">
                    {products.length === 0 
                      ? 'Start building your product catalog by adding your first product.'
                      : 'Try adjusting your search or filter criteria.'
                    }
                  </p>
                  {products.length === 0 && (
                    <Link href="/dashboard/products/create">
                      <Button className="bg-blue-600 hover:bg-blue-700">
                        <Plus className="w-4 h-4 mr-2" />
                        Add Your First Product
                      </Button>
                    </Link>
                  )}
                </CardContent>
              </Card>
            ) : (
              <div className={viewMode === 'grid' 
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                : 'space-y-4'
              }>
                {filteredProducts.map(product => (
                  <Card key={product.id} className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-4">
                      {viewMode === 'grid' ? (
                        // Grid View
                        <div>
                          {/* Product Image */}
                          <div className="aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                            {product.images.find(img => img.isMain) ? (
                              <img
                                src={product.images.find(img => img.isMain)?.url}
                                alt={product.name}
                                className="w-full h-full object-cover rounded-lg"
                              />
                            ) : (
                              <Package className="w-12 h-12 text-gray-400" />
                            )}
                          </div>

                          {/* Product Info */}
                          <div className="space-y-2">
                            <div className="flex items-start justify-between">
                              <h3 className="font-semibold text-gray-900 line-clamp-2">
                                {product.name}
                              </h3>
                              {product.isFeatured && (
                                <Star className="w-4 h-4 text-yellow-500 flex-shrink-0 ml-2" />
                              )}
                            </div>

                            <p className="text-sm text-gray-600 line-clamp-2">
                              {product.shortDescription || product.description}
                            </p>

                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-1">
                                <DollarSign className="w-4 h-4 text-gray-400" />
                                <span className="font-semibold text-gray-900">
                                  {Number(product.basePrice).toFixed(2)} {product.currency}
                                </span>
                              </div>
                              <span className="text-sm text-gray-500">
                                MOQ: {product.minOrderQty}
                              </span>
                            </div>

                            <div className="flex items-center justify-between">
                              <Badge className={getStatusColor(product.status)}>
                                {product.status}
                              </Badge>
                              <Badge className={getStockStatusColor(product.stockStatus)}>
                                {product.stockStatus}
                              </Badge>
                            </div>

                            <ProductActionButtons product={product} onDelete={handleDeleteProduct} />
                          </div>
                        </div>
                      ) : (
                        // List View
                        <div className="flex items-center space-x-4">
                          <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            {product.images.find(img => img.isMain) ? (
                              <img
                                src={product.images.find(img => img.isMain)?.url}
                                alt={product.name}
                                className="w-full h-full object-cover rounded-lg"
                              />
                            ) : (
                              <Package className="w-8 h-8 text-gray-400" />
                            )}
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                              <h3 className="font-semibold text-gray-900 truncate">
                                {product.name}
                              </h3>
                              {product.isFeatured && (
                                <Star className="w-4 h-4 text-yellow-500 flex-shrink-0" />
                              )}
                            </div>
                            <p className="text-sm text-gray-600 truncate">
                              {product.category.name} • SKU: {product.sku || 'N/A'}
                            </p>
                            <div className="flex items-center space-x-4 mt-1">
                              <span className="text-sm font-medium text-gray-900">
                                ${Number(product.basePrice).toFixed(2)} {product.currency}
                              </span>
                              <Badge className={getStatusColor(product.status)}>
                                {product.status}
                              </Badge>
                              <Badge className={getStockStatusColor(product.stockStatus)}>
                                Stock: {product.stockQuantity}
                              </Badge>
                            </div>
                          </div>

                          <ProductActionButtons product={product} onDelete={handleDeleteProduct} />
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
