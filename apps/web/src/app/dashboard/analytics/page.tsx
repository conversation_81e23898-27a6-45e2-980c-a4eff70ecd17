'use client';

import { useState } from 'react';
import { useAuth } from '../../../contexts/auth-context';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../components/dashboard/dashboard-sidebar';
import { But<PERSON> } from '../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Badge } from '../../../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Package,
  ShoppingCart,
  Users,
  DollarSign,
  Calendar,
  Download,
  Filter,
  Refresh<PERSON>w,
  Eye,
  Copy,
  Star,
  Clock,
  Target,
  Award,
  Activity
} from 'lucide-react';
import { trpc } from '../../../lib/trpc';

// Mock data for demonstration
const templateUsageData = [
  { month: 'Jan', templates: 45, orders: 234, revenue: 12500 },
  { month: 'Feb', templates: 52, orders: 289, revenue: 15200 },
  { month: 'Mar', templates: 48, orders: 267, revenue: 14100 },
  { month: 'Apr', templates: 61, orders: 345, revenue: 18900 },
  { month: 'May', templates: 58, orders: 312, revenue: 17600 },
  { month: 'Jun', templates: 67, orders: 398, revenue: 21300 },
];

const templateTypeData = [
  { name: 'Standard', value: 45, color: '#3B82F6' },
  { name: 'Recurring', value: 28, color: '#10B981' },
  { name: 'Customer', value: 18, color: '#8B5CF6' },
  { name: 'Seasonal', value: 9, color: '#F59E0B' },
];

const topTemplates = [
  {
    id: '1',
    name: 'Electronics Starter Kit',
    type: 'STANDARD',
    uses: 156,
    revenue: 4680,
    conversionRate: 78,
    trend: 'up',
    change: '+12%',
  },
  {
    id: '2',
    name: 'Monthly Textile Supplies',
    type: 'RECURRING',
    uses: 234,
    revenue: 7020,
    conversionRate: 85,
    trend: 'up',
    change: '+8%',
  },
  {
    id: '3',
    name: 'Office Furniture Bundle',
    type: 'CUSTOMER',
    uses: 78,
    revenue: 2340,
    conversionRate: 65,
    trend: 'down',
    change: '-3%',
  },
  {
    id: '4',
    name: 'Holiday Decorations',
    type: 'SEASONAL',
    uses: 45,
    revenue: 1350,
    conversionRate: 72,
    trend: 'up',
    change: '+15%',
  },
];

const performanceMetrics = [
  {
    title: 'Total Templates',
    value: '127',
    change: '+8.2%',
    trend: 'up',
    icon: Package,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
  },
  {
    title: 'Template Uses',
    value: '2,847',
    change: '+12.5%',
    trend: 'up',
    icon: Copy,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
  },
  {
    title: 'Generated Revenue',
    value: '$89,420',
    change: '+15.3%',
    trend: 'up',
    icon: DollarSign,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
  },
  {
    title: 'Avg Conversion Rate',
    value: '74.2%',
    change: '+2.1%',
    trend: 'up',
    icon: Target,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
  },
];

const recentActivity = [
  {
    id: '1',
    type: 'template_used',
    template: 'Electronics Starter Kit',
    customer: 'TechCorp Inc.',
    value: '$299.99',
    time: '2 hours ago',
  },
  {
    id: '2',
    type: 'template_created',
    template: 'Custom PCB Assembly',
    creator: 'John Smith',
    time: '4 hours ago',
  },
  {
    id: '3',
    type: 'recurring_order',
    template: 'Monthly Textile Supplies',
    customer: 'Fashion Forward Ltd.',
    value: '$2,499.99',
    time: '6 hours ago',
  },
  {
    id: '4',
    type: 'template_shared',
    template: 'Office Furniture Bundle',
    shares: 12,
    time: '8 hours ago',
  },
];

export default function AnalyticsPage() {
  const { user } = useAuth();
  const [dateRange, setDateRange] = useState('30d');
  const [selectedMetric, setSelectedMetric] = useState('usage');

  // This would be actual tRPC queries for analytics data
  const { data: analyticsData, isLoading, refetch } = trpc.analytics?.getTemplateAnalytics?.useQuery({
    dateRange: dateRange as '7d' | '30d' | '90d' | '1y',
    metric: selectedMetric as 'usage' | 'revenue' | 'conversion',
  }) || { data: null, isLoading: false, refetch: () => {} };

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <BarChart className="w-6 h-6 mr-3 text-blue-600" />
                  Analytics Dashboard
                </h1>
                <p className="text-gray-600">Template performance and business insights</p>
              </div>
              
              <div className="flex items-center space-x-3">
                <select
                  value={dateRange}
                  onChange={(e) => setDateRange(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  aria-label="Select date range"
                >
                  <option value="7d">Last 7 days</option>
                  <option value="30d">Last 30 days</option>
                  <option value="90d">Last 90 days</option>
                  <option value="1y">Last year</option>
                </select>
                
                <Button
                  onClick={() => refetch()}
                  variant="outline"
                  disabled={isLoading}
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
                
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Download className="w-4 h-4 mr-2" />
                  Export Report
                </Button>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            <div className="space-y-6">
              {/* Performance Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {performanceMetrics.map((metric) => {
                  const Icon = metric.icon;
                  return (
                    <Card key={metric.title} className="hover:shadow-md transition-shadow">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-gray-600">
                          {metric.title}
                        </CardTitle>
                        <div className={`p-2 rounded-lg ${metric.bgColor}`}>
                          <Icon className={`w-4 h-4 ${metric.color}`} />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-gray-900">
                          {metric.value}
                        </div>
                        <div className="flex items-center mt-1">
                          <span className={`text-xs font-medium ${
                            metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {metric.trend === 'up' ? (
                              <TrendingUp className="w-3 h-3 inline mr-1" />
                            ) : (
                              <TrendingDown className="w-3 h-3 inline mr-1" />
                            )}
                            {metric.change}
                          </span>
                          <span className="text-xs text-gray-500 ml-1">
                            from last period
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {/* Charts Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Usage Trends */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Activity className="w-5 h-5 mr-2" />
                      Template Usage Trends
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <AreaChart data={templateUsageData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Area
                          type="monotone"
                          dataKey="orders"
                          stackId="1"
                          stroke="#3B82F6"
                          fill="#3B82F6"
                          fillOpacity={0.6}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Template Types Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Package className="w-5 h-5 mr-2" />
                      Template Types Distribution
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={templateTypeData}
                          cx="50%"
                          cy="50%"
                          outerRadius={100}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {templateTypeData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>

              {/* Detailed Analytics Tabs */}
              <Tabs defaultValue="templates" className="space-y-6">
                <TabsList>
                  <TabsTrigger value="templates">Top Templates</TabsTrigger>
                  <TabsTrigger value="performance">Performance</TabsTrigger>
                  <TabsTrigger value="activity">Recent Activity</TabsTrigger>
                  <TabsTrigger value="insights">Insights</TabsTrigger>
                </TabsList>

                <TabsContent value="templates">
                  <TopTemplatesTab templates={topTemplates} />
                </TabsContent>

                <TabsContent value="performance">
                  <PerformanceTab data={templateUsageData} />
                </TabsContent>

                <TabsContent value="activity">
                  <ActivityTab activities={recentActivity} />
                </TabsContent>

                <TabsContent value="insights">
                  <InsightsTab />
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}

function TopTemplatesTab({ templates }: { templates: any[] }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Award className="w-5 h-5 mr-2" />
          Top Performing Templates
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {templates.map((template, index) => (
            <div key={template.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
              <div className="flex items-center space-x-4">
                <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full font-semibold text-sm">
                  #{index + 1}
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{template.name}</h4>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge variant="outline" className="text-xs">
                      {template.type}
                    </Badge>
                    <span className="text-sm text-gray-500">
                      {template.uses} uses
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-6 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-gray-900">
                    ${template.revenue.toLocaleString()}
                  </div>
                  <div className="text-gray-500">Revenue</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-gray-900">
                    {template.conversionRate}%
                  </div>
                  <div className="text-gray-500">Conversion</div>
                </div>
                <div className="text-center">
                  <div className={`font-semibold flex items-center ${
                    template.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {template.trend === 'up' ? (
                      <TrendingUp className="w-4 h-4 mr-1" />
                    ) : (
                      <TrendingDown className="w-4 h-4 mr-1" />
                    )}
                    {template.change}
                  </div>
                  <div className="text-gray-500">Trend</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function PerformanceTab({ data }: { data: any[] }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Revenue Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="w-5 h-5 mr-2" />
            Revenue Trends
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [`$${value}`, 'Revenue']} />
              <Line
                type="monotone"
                dataKey="revenue"
                stroke="#10B981"
                strokeWidth={3}
                dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Template Usage Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Copy className="w-5 h-5 mr-2" />
            Template Usage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="templates" fill="#3B82F6" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}

function ActivityTab({ activities }: { activities: any[] }) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'template_used':
        return <Copy className="w-4 h-4 text-blue-600" />;
      case 'template_created':
        return <Package className="w-4 h-4 text-green-600" />;
      case 'recurring_order':
        return <RefreshCw className="w-4 h-4 text-purple-600" />;
      case 'template_shared':
        return <Users className="w-4 h-4 text-orange-600" />;
      default:
        return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const getActivityDescription = (activity: any) => {
    switch (activity.type) {
      case 'template_used':
        return `${activity.customer} used template "${activity.template}" for ${activity.value}`;
      case 'template_created':
        return `${activity.creator} created new template "${activity.template}"`;
      case 'recurring_order':
        return `${activity.customer} generated recurring order from "${activity.template}" for ${activity.value}`;
      case 'template_shared':
        return `Template "${activity.template}" was shared ${activity.shares} times`;
      default:
        return 'Unknown activity';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="w-5 h-5 mr-2" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg">
              <div className="flex-shrink-0 mt-1">
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm text-gray-900">
                  {getActivityDescription(activity)}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {activity.time}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function InsightsTab() {
  const insights = [
    {
      title: 'Top Performing Category',
      value: 'Electronics',
      description: 'Electronics templates generate 45% more revenue than average',
      icon: TrendingUp,
      color: 'text-green-600',
    },
    {
      title: 'Best Conversion Time',
      value: 'Tuesday 2-4 PM',
      description: 'Templates used during this time have 23% higher conversion rates',
      icon: Clock,
      color: 'text-blue-600',
    },
    {
      title: 'Recurring Revenue Impact',
      value: '34% of total revenue',
      description: 'Recurring templates contribute significantly to stable income',
      icon: RefreshCw,
      color: 'text-purple-600',
    },
    {
      title: 'Customer Retention',
      value: '78% return rate',
      description: 'Customers who use templates are more likely to return',
      icon: Users,
      color: 'text-orange-600',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {insights.map((insight, index) => {
        const Icon = insight.icon;
        return (
          <Card key={index}>
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <Icon className={`w-5 h-5 mr-2 ${insight.color}`} />
                {insight.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900 mb-2">
                {insight.value}
              </div>
              <p className="text-gray-600 text-sm">
                {insight.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
