/* THIS FILE WAS GENERATED AUTOMATICALLY BY PAYLOAD. */
import type { Metadata } from 'next';

import config from '../../payload/payload.config';
import { RootLayout } from '@payloadcms/next/layouts';
import { importMap } from './admin/importMap.js';
// ServerFunctionClient type will be inferred

import './custom.scss';

type Args = {
  children: React.ReactNode;
};

export const metadata: Metadata = {
  title: 'FC-CHINA Factory Portal Admin',
  description: 'Factory management and content administration',
  icons: [{ url: '/favicon.ico' }],
};

const Layout = ({ children }: Args) => {
  const serverFunction = async (args: any) => {
    'use server'
    // Server function implementation for Payload CMS
    return { success: true }
  }

  return (
    <RootLayout
      config={Promise.resolve(config)}
      importMap={importMap}
      serverFunction={serverFunction}
    >
      {children}
    </RootLayout>
  )
};

export default Layout;
