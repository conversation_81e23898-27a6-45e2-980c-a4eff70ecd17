/**
 * FC-CHINA Payload CMS Integration
 * Handles data flow between onboarding wizard and Payload CMS collections
 * Following hybrid architecture: Payload CMS for admin, Supabase for real-time
 */

import { z } from 'zod';

// Import Payload types
import type { Factory, User } from '../payload/payload-types';

// Onboarding data schemas (matching the wizard)
const basicInfoSchema = z.object({
  factoryName: z.string().min(2, 'Factory name must be at least 2 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  industry: z.string().min(1, 'Please select an industry'),
  contactEmail: z.string().email('Please enter a valid email'),
  contactPhone: z.string().min(10, 'Please enter a valid phone number'),
  address: z.string().min(5, 'Please enter a complete address'),
  city: z.string().min(2, 'Please enter a city'),
  country: z.string().min(2, 'Please select a country'),
  timezone: z.string().min(1, 'Please select a timezone'),
});

const businessDetailsSchema = z.object({
  businessType: z.string().min(1, 'Please select a business type'),
  businessLicense: z.string().optional(),
  taxId: z.string().min(5, 'Please enter a valid tax ID'),
  establishedYear: z.number().min(1900).max(new Date().getFullYear()),
  employeeCount: z.string().min(1, 'Please select employee count range'),
  annualRevenue: z.string().optional(),
});

const factoryConfigSchema = z.object({
  productCategories: z.array(z.string()).min(1, 'Please select at least one product category'),
  productionCapacity: z.string().min(1, 'Please enter production capacity'),
  capacityUnit: z.string().min(1, 'Please select capacity unit'),
  shippingMethods: z.array(z.string()).min(1, 'Please select at least one shipping method'),
  paymentMethods: z.array(z.string()).min(1, 'Please select at least one payment method'),
  certifications: z.array(z.string()).optional(),
  minimumOrderQuantity: z.string().min(1, 'Please enter minimum order quantity'),
  moqUnit: z.string().min(1, 'Please select MOQ unit'),
  leadTime: z.string().min(1, 'Please enter typical lead time'),
});

const adminSetupSchema = z.object({
  adminName: z.string().min(2, 'Admin name must be at least 2 characters'),
  adminEmail: z.string().email('Please enter a valid email'),
  adminPhone: z.string().min(10, 'Please enter a valid phone number'),
  adminRole: z.string().min(1, 'Please select an admin role'),
  department: z.string().min(1, 'Please enter department'),
  teamMembers: z.array(z.object({
    name: z.string().min(2, 'Name must be at least 2 characters'),
    email: z.string().email('Please enter a valid email'),
    role: z.string().min(1, 'Please select a role'),
  })).optional(),
});

const verificationSchema = z.object({
  termsAccepted: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
  privacyAccepted: z.boolean().refine(val => val === true, 'You must accept the privacy policy'),
  documentsUploaded: z.boolean().refine(val => val === true, 'Please upload all required documents'),
});

// Combined onboarding data schema
const factoryOnboardingSchema = z.object({
  basicInfo: basicInfoSchema,
  businessDetails: businessDetailsSchema,
  factoryConfig: factoryConfigSchema,
  adminSetup: adminSetupSchema,
  verification: verificationSchema,
});

export type FactoryOnboardingData = z.infer<typeof factoryOnboardingSchema>;

/**
 * Maps onboarding wizard data to Payload CMS Factory collection format
 * @param onboardingData - Data collected from the 5-step onboarding wizard
 * @param userId - Auth0 user ID for the factory owner
 * @returns Factory data ready for Payload CMS creation
 */
export function mapOnboardingToPayloadFactory(
  onboardingData: FactoryOnboardingData,
  userId: string
): Partial<Factory> {
  const { basicInfo, businessDetails, factoryConfig, adminSetup, verification } = onboardingData;

  // Helper function to parse employee count string to number
  const parseEmployeeCount = (employeeCountStr: string): number => {
    const mapping: Record<string, number> = {
      '1-10 employees': 5,
      '11-50 employees': 30,
      '51-200 employees': 125,
      '201-500 employees': 350,
      '501-1000 employees': 750,
      '1000+ employees': 1500,
    };
    return mapping[employeeCountStr] || 1;
  };

  // Generate unique slug from factory name
  const generateSlug = (name: string): string => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')
      .substring(0, 50);
  };

  return {
    name: basicInfo.factoryName,
    slug: generateSlug(basicInfo.factoryName),
    description: basicInfo.description,
    
    // Contact Information (admin info overrides basic info)
    email: adminSetup.adminEmail || basicInfo.contactEmail,
    phone: adminSetup.adminPhone || basicInfo.contactPhone,
    addressStreet: basicInfo.address,
    addressCity: basicInfo.city,
    addressCountry: basicInfo.country,

    // Business Information
    businessLicense: businessDetails.businessLicense || '',
    taxId: businessDetails.taxId,
    establishedYear: businessDetails.establishedYear,
    employeeCount: parseEmployeeCount(businessDetails.employeeCount),
    
    // Status and verification
    status: 'active',
    verificationStatus: verification.termsAccepted && verification.privacyAccepted && verification.documentsUploaded
      ? 'pending'
      : 'unverified',
    subscriptionTier: 'free',
  };
}

/**
 * Creates a factory in Payload CMS using the REST API
 * @param factoryData - Factory data from onboarding wizard
 * @returns Promise with created factory data
 */
export async function createFactoryInPayload(factoryData: Partial<Factory>): Promise<Factory> {
  const payloadUrl = process.env.NEXT_PUBLIC_PAYLOAD_URL || 'http://localhost:3000';
  
  try {
    const response = await fetch(`${payloadUrl}/api/factories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(factoryData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to create factory: ${errorData.message || response.statusText}`);
    }

    const createdFactory = await response.json();
    console.log('✅ Factory created in Payload CMS:', createdFactory);
    
    return createdFactory;
  } catch (error) {
    console.error('❌ Error creating factory in Payload CMS:', error);
    throw error;
  }
}

/**
 * Retrieves factory data from Payload CMS
 * @param factoryId - Factory ID or slug
 * @returns Promise with factory data
 */
export async function getFactoryFromPayload(factoryId: string): Promise<Factory | null> {
  const payloadUrl = process.env.NEXT_PUBLIC_PAYLOAD_URL || 'http://localhost:3000';
  
  try {
    const response = await fetch(`${payloadUrl}/api/factories/${factoryId}`);
    
    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch factory: ${response.statusText}`);
    }

    const factory = await response.json();
    console.log('✅ Factory retrieved from Payload CMS:', factory);
    
    return factory;
  } catch (error) {
    console.error('❌ Error retrieving factory from Payload CMS:', error);
    return null;
  }
}

/**
 * Updates factory data in Payload CMS
 * @param factoryId - Factory ID
 * @param updateData - Partial factory data to update
 * @returns Promise with updated factory data
 */
export async function updateFactoryInPayload(
  factoryId: string, 
  updateData: Partial<Factory>
): Promise<Factory> {
  const payloadUrl = process.env.NEXT_PUBLIC_PAYLOAD_URL || 'http://localhost:3000';
  
  try {
    const response = await fetch(`${payloadUrl}/api/factories/${factoryId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to update factory: ${errorData.message || response.statusText}`);
    }

    const updatedFactory = await response.json();
    console.log('✅ Factory updated in Payload CMS:', updatedFactory);
    
    return updatedFactory;
  } catch (error) {
    console.error('❌ Error updating factory in Payload CMS:', error);
    throw error;
  }
}

/**
 * Stores onboarding completion status in localStorage (temporary until Payload integration)
 * @param factoryId - Created factory ID
 */
export function markOnboardingComplete(factoryId: string): void {
  localStorage.setItem('fc-china-onboarding-complete', 'true');
  localStorage.setItem('fc-china-factory-id', factoryId);
  console.log('✅ Onboarding marked as complete for factory:', factoryId);
}

/**
 * Checks if onboarding has been completed
 * @returns boolean indicating if onboarding is complete
 */
export function isOnboardingComplete(): boolean {
  return localStorage.getItem('fc-china-onboarding-complete') === 'true';
}

/**
 * Gets the current factory ID from localStorage
 * @returns Factory ID or null
 */
export function getCurrentFactoryId(): string | null {
  return localStorage.getItem('fc-china-factory-id');
}

/**
 * Clears onboarding data (for testing or reset purposes)
 */
export function clearOnboardingData(): void {
  localStorage.removeItem('fc-china-onboarding-complete');
  localStorage.removeItem('fc-china-factory-id');
  console.log('🧹 Onboarding data cleared');
}
