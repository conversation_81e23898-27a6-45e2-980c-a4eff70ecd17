import { GlobalConfig } from 'payload/types';
import { isFactoryOwnerOrAdmin } from '../access/users';

export const FactorySettings: GlobalConfig = {
  slug: 'factory-settings',
  admin: {
    group: 'Settings',
  },
  access: {
    read: isFactoryOwnerOrAdmin,
    update: isFactoryOwnerOrAdmin,
  },
  fields: [
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General Settings',
          fields: [
            {
              name: 'siteName',
              type: 'text',
              defaultValue: 'FC-CHINA Factory Portal',
            },
            {
              name: 'siteDescription',
              type: 'textarea',
              defaultValue: 'Connect Chinese factories with global customers',
            },
            {
              name: 'contactEmail',
              type: 'email',
            },
            {
              name: 'supportPhone',
              type: 'text',
            },
            {
              name: 'defaultLanguage',
              type: 'select',
              defaultValue: 'en',
              options: [
                { label: 'English', value: 'en' },
                { label: '简体中文', value: 'zh-CN' },
                { label: '繁體中文', value: 'zh-TW' },
              ],
            },
            {
              name: 'defaultCurrency',
              type: 'select',
              defaultValue: 'USD',
              options: [
                { label: 'US Dollar (USD)', value: 'USD' },
                { label: 'Chinese Yuan (CNY)', value: 'CNY' },
                { label: 'Euro (EUR)', value: 'EUR' },
              ],
            },
          ],
        },
        {
          label: 'Email Settings',
          fields: [
            {
              name: 'emailProvider',
              type: 'select',
              defaultValue: 'sendgrid',
              options: [
                { label: 'SendGrid', value: 'sendgrid' },
                { label: 'Mailgun', value: 'mailgun' },
                { label: 'SMTP', value: 'smtp' },
              ],
            },
            {
              name: 'fromEmail',
              type: 'email',
              defaultValue: '<EMAIL>',
            },
            {
              name: 'fromName',
              type: 'text',
              defaultValue: 'FC-CHINA',
            },
            {
              name: 'welcomeEmailEnabled',
              type: 'checkbox',
              defaultValue: true,
            },
            {
              name: 'orderNotificationsEnabled',
              type: 'checkbox',
              defaultValue: true,
            },
          ],
        },
        {
          label: 'Payment Settings',
          fields: [
            {
              name: 'paymentMethods',
              type: 'array',
              fields: [
                {
                  name: 'method',
                  type: 'select',
                  required: true,
                  options: [
                    { label: 'Bank Transfer', value: 'bank_transfer' },
                    { label: 'PayPal', value: 'paypal' },
                    { label: 'Stripe', value: 'stripe' },
                    { label: 'Alipay', value: 'alipay' },
                    { label: 'WeChat Pay', value: 'wechat_pay' },
                  ],
                },
                {
                  name: 'isEnabled',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'displayName',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'description',
                  type: 'textarea',
                },
              ],
            },
            {
              name: 'taxRate',
              type: 'number',
              min: 0,
              max: 100,
              defaultValue: 0,
              admin: {
                description: 'Tax rate as percentage (e.g., 13 for 13%)',
              },
            },
          ],
        },
        {
          label: 'Shipping Settings',
          fields: [
            {
              name: 'shippingMethods',
              type: 'array',
              fields: [
                {
                  name: 'name',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'description',
                  type: 'textarea',
                },
                {
                  name: 'estimatedDays',
                  type: 'number',
                  min: 1,
                },
                {
                  name: 'cost',
                  type: 'number',
                  min: 0,
                },
                {
                  name: 'isEnabled',
                  type: 'checkbox',
                  defaultValue: true,
                },
              ],
            },
            {
              name: 'freeShippingThreshold',
              type: 'number',
              min: 0,
              admin: {
                description: 'Minimum order amount for free shipping',
              },
            },
          ],
        },
        {
          label: 'API Settings',
          fields: [
            {
              name: 'apiRateLimit',
              type: 'number',
              defaultValue: 1000,
              min: 100,
              admin: {
                description: 'API requests per hour per user',
              },
            },
            {
              name: 'webhookUrl',
              type: 'text',
              admin: {
                description: 'URL to receive webhook notifications',
              },
            },
            {
              name: 'allowedOrigins',
              type: 'array',
              fields: [
                {
                  name: 'origin',
                  type: 'text',
                  required: true,
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};
