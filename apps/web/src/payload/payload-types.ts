/**
 * Temporary Payload CMS Types
 * This file provides minimal type definitions for Payload CMS integration
 * TODO: Generate proper types when Payload CMS is fully configured
 */

export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  factoryId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Factory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  coverImage?: string;
  businessLicense?: string;
  taxId?: string;
  establishedYear?: number;
  employeeCount?: number;
  annualRevenue?: number;
  email: string;
  phone?: string;
  website?: string;
  addressStreet?: string;
  addressCity?: string;
  addressState?: string;
  addressPostalCode?: string;
  addressCountry?: string;
  currency?: string;
  timezone?: string;
  language?: string;
  verificationStatus?: string;
  status?: string;
  subscriptionTier?: string;
  subscriptionEndsAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Product {
  id: string;
  name: string;
  description?: string;
  category?: string;
  price?: number;
  images?: string[];
  factoryId: string;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Media {
  id: string;
  filename: string;
  mimeType: string;
  filesize: number;
  url: string;
  createdAt: string;
  updatedAt: string;
}

// Payload CMS Collection Types
export interface Collections {
  users: User;
  factories: Factory;
  products: Product;
  categories: Category;
  media: Media;
}

// Payload CMS Global Types
export interface Globals {
  factorySettings: {
    id: string;
    siteName?: string;
    siteDescription?: string;
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
  };
}
