import { Strategy as Auth0Strategy } from 'passport-auth0';
import { Payload } from 'payload';

export interface Auth0User {
  sub: string;
  email: string;
  given_name?: string;
  family_name?: string;
  name?: string;
  picture?: string;
  email_verified?: boolean;
  [key: string]: any;
}

export const createAuth0Strategy = (payload: Payload) => {
  return new Auth0Strategy(
    {
      domain: process.env.AUTH0_DOMAIN!,
      clientID: process.env.AUTH0_CLIENT_ID!,
      clientSecret: process.env.AUTH0_CLIENT_SECRET!,
      callbackURL: process.env.AUTH0_CALLBACK_URL || '/api/auth/callback',
      passReqToCallback: true,
    },
    async (accessToken: string, refreshToken: string, extraParams: any, profile: Auth0User, done: any) => {
      try {
        // Extract user information from Auth0 profile
        const auth0Id = profile.sub;
        const email = profile.email;
        const firstName = profile.given_name || profile.name?.split(' ')[0] || '';
        const lastName = profile.family_name || profile.name?.split(' ').slice(1).join(' ') || '';
        const avatar = profile.picture;

        // Check if user already exists in Payload CMS
        const existingUsers = await payload.find({
          collection: 'users',
          where: {
            auth0Id: {
              equals: auth0Id,
            },
          },
          limit: 1,
        });

        let user;

        if (existingUsers.docs.length > 0) {
          // User exists, update their information
          user = await payload.update({
            collection: 'users',
            id: existingUsers.docs[0].id,
            data: {
              email,
              firstName,
              lastName,
              avatar,
              lastLoginAt: new Date(),
            },
          });
        } else {
          // Check if user exists by email (for migration purposes)
          const existingByEmail = await payload.find({
            collection: 'users',
            where: {
              email: {
                equals: email,
              },
            },
            limit: 1,
          });

          if (existingByEmail.docs.length > 0) {
            // User exists by email, link Auth0 ID
            user = await payload.update({
              collection: 'users',
              id: existingByEmail.docs[0].id,
              data: {
                auth0Id,
                firstName,
                lastName,
                avatar,
                lastLoginAt: new Date(),
              },
            });
          } else {
            // Create new user
            user = await payload.create({
              collection: 'users',
              data: {
                auth0Id,
                email,
                firstName,
                lastName,
                avatar,
                role: 'factory_staff', // Default role
                language: 'en',
                timezone: 'UTC',
                isActive: true,
                lastLoginAt: new Date(),
              },
            });
          }
        }

        return done(null, user);
      } catch (error) {
        console.error('Auth0 Strategy Error:', error);
        return done(error, null);
      }
    }
  );
};

// Helper function to sync Auth0 user with Payload CMS
export const syncAuth0User = async (payload: Payload, auth0User: Auth0User) => {
  try {
    const auth0Id = auth0User.sub;
    const email = auth0User.email;
    const firstName = auth0User.given_name || auth0User.name?.split(' ')[0] || '';
    const lastName = auth0User.family_name || auth0User.name?.split(' ').slice(1).join(' ') || '';
    const avatar = auth0User.picture;

    // Check if user exists
    const existingUsers = await payload.find({
      collection: 'users',
      where: {
        auth0Id: {
          equals: auth0Id,
        },
      },
      limit: 1,
    });

    if (existingUsers.docs.length > 0) {
      // Update existing user
      return await payload.update({
        collection: 'users',
        id: existingUsers.docs[0].id,
        data: {
          email,
          firstName,
          lastName,
          avatar,
          lastLoginAt: new Date(),
        },
      });
    } else {
      // Check by email for migration
      const existingByEmail = await payload.find({
        collection: 'users',
        where: {
          email: {
            equals: email,
          },
        },
        limit: 1,
      });

      if (existingByEmail.docs.length > 0) {
        // Link Auth0 ID to existing user
        return await payload.update({
          collection: 'users',
          id: existingByEmail.docs[0].id,
          data: {
            auth0Id,
            firstName,
            lastName,
            avatar,
            lastLoginAt: new Date(),
          },
        });
      } else {
        // Create new user
        return await payload.create({
          collection: 'users',
          data: {
            auth0Id,
            email,
            firstName,
            lastName,
            avatar,
            role: 'factory_staff',
            language: 'en',
            timezone: 'UTC',
            isActive: true,
            lastLoginAt: new Date(),
          },
        });
      }
    }
  } catch (error) {
    console.error('Error syncing Auth0 user:', error);
    throw error;
  }
};

// Helper function to get user by Auth0 ID
export const getUserByAuth0Id = async (payload: Payload, auth0Id: string) => {
  try {
    const users = await payload.find({
      collection: 'users',
      where: {
        auth0Id: {
          equals: auth0Id,
        },
      },
      limit: 1,
    });

    return users.docs.length > 0 ? users.docs[0] : null;
  } catch (error) {
    console.error('Error getting user by Auth0 ID:', error);
    return null;
  }
};
