
> web@0.1.0 type-check
> tsc --noEmit

src/app/dashboard/recurring-orders/page.tsx(278,38): error TS2345: Argument of type '(recurringOrder: RecurringOrder) => JSX.Element' is not assignable to parameter of type '(value: { description: string | null; name: string; id: string; createdAt: string; updatedAt: string; createdBy: string; template: { name: string; id: string; templateType: TemplateType; defaultCurrency: Currency; estimatedTotalAmount: number | null; }; ... 22 more ...; notifyFactory: boolean; }, index: number, arra...'.
  Types of parameters 'recurringOrder' and 'value' are incompatible.
    Type '{ description: string | null; name: string; id: string; createdAt: string; updatedAt: string; createdBy: string; template: { name: string; id: string; templateType: TemplateType; defaultCurrency: Currency; estimatedTotalAmount: number | null; }; ... 22 more ...; notifyFactory: boolean; }' is not assignable to type 'RecurringOrder'.
      Types of property 'description' are incompatible.
        Type 'string | null' is not assignable to type 'string | undefined'.
          Type 'null' is not assignable to type 'string | undefined'.
src/app/dashboard/templates/[id]/edit/page.tsx(127,9): error TS2322: Type '{ id: string; productId: string; variantId: string | null; quantity: number; unitPrice: number | null; specifications: string | number | boolean | (string | number | boolean | (string | ... 4 more ... | null)[] | Record<...> | null)[] | Record<...> | null; notes: string | null; sortOrder: number; product: { ...; }; ...' is not assignable to type 'TemplateItem[]'.
  Type '{ id: string; productId: string; variantId: string | null; quantity: number; unitPrice: number | null; specifications: string | number | boolean | (string | number | boolean | (string | ... 4 more ... | null)[] | Record<...> | null)[] | Record<...> | null; notes: string | null; sortOrder: number; product: { ...; }; ...' is not assignable to type 'TemplateItem'.
    Types of property 'variantId' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
src/app/dashboard/templates/page.tsx(268,21): error TS2322: Type '{ description: string | null; name: string; id: string; createdAt: string; updatedAt: string; createdBy: string; factoryId: string; _count: { orders: number; recurringOrders: number; }; isActive: boolean; ... 15 more ...; estimatedTotalAmount: number | null; }' is not assignable to type 'OrderTemplate'.
  Types of property 'description' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.
src/app/test/integration/page.tsx(129,17): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
src/app/test/integration/page.tsx(129,41): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
src/app/test/product-edit-integration/page.tsx(253,22): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
src/app/test/product-edit-integration/page.tsx(262,22): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
src/app/test/product-edit-integration/page.tsx(271,22): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
src/app/test/product-edit-integration/page.tsx(280,22): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
src/app/test/product-edit-integration/page.tsx(289,22): error TS2774: This condition will always return true since this function is always defined. Did you mean to call it instead?
src/components/auth/payload-admin-button.tsx(11,5): error TS2339: Property 'payloadToken' does not exist on type 'AuthContextType'.
src/components/auth/payload-admin-button.tsx(30,22): error TS2339: Property 'factory' does not exist on type '{ firstName: string; lastName: string; role: string; avatar?: string | undefined; }'.
src/components/auth/payload-admin-button.tsx(70,24): error TS2339: Property 'payloadToken' does not exist on type 'AuthContextType'.
src/components/auth/payload-admin-button.tsx(103,5): error TS2339: Property 'payloadToken' does not exist on type 'AuthContextType'.
src/components/auth/payload-admin-button.tsx(141,23): error TS2339: Property 'factory' does not exist on type '{ firstName: string; lastName: string; role: string; avatar?: string | undefined; }'.
src/components/auth/payload-admin-button.tsx(143,52): error TS2339: Property 'factory' does not exist on type '{ firstName: string; lastName: string; role: string; avatar?: string | undefined; }'.
src/components/dashboard/dashboard-overview.tsx(89,37): error TS2339: Property 'avatar' does not exist on type 'User'.
src/components/dashboard/dashboard-overview.tsx(89,56): error TS2339: Property 'firstName' does not exist on type 'User'.
src/components/dashboard/dashboard-sidebar.tsx(83,37): error TS2339: Property 'avatar' does not exist on type 'User'.
src/components/dashboard/dashboard-sidebar.tsx(83,56): error TS2339: Property 'firstName' does not exist on type 'User'.
src/components/dashboard/message-attachment.tsx(259,20): error TS7006: Parameter 'prev' implicitly has an 'any' type.
src/components/dashboard/message-attachment.tsx(259,20): error TS2345: Argument of type '(prev: any) => any' is not assignable to parameter of type 'any[]'.
src/components/dashboard/message-attachment.tsx(259,40): error TS7006: Parameter 'a' implicitly has an 'any' type.
src/components/dashboard/new-conversation-dialog.tsx(60,31): error TS2589: Type instantiation is excessively deep and possibly infinite.
src/components/forms/product-form-validation.tsx(48,30): error TS2345: Argument of type 'string' is not assignable to parameter of type 'requiredKeys<{ name: string; sku: string; description: string | undefined; price: number; currency: string | undefined; moq: number; status: "ACTIVE" | "INACTIVE" | "DRAFT" | "DISCONTINUED" | undefined; ... 17 more ...; keyFeatures: string | undefined; }> | optionalKeys<...>'.
src/components/forms/product-form-validation.tsx(49,28): error TS2345: Argument of type 'string' is not assignable to parameter of type 'requiredKeys<{ name: string; sku: string; description: string | undefined; price: number; currency: string | undefined; moq: number; status: "ACTIVE" | "INACTIVE" | "DRAFT" | "DISCONTINUED" | undefined; ... 17 more ...; keyFeatures: string | undefined; }> | optionalKeys<...>'.
src/components/onboarding/factory-onboarding-wizard.tsx(255,53): error TS2339: Property 'auth0Id' does not exist on type 'User'.
src/components/onboarding/factory-onboarding-wizard.tsx(260,28): error TS2339: Property 'auth0Id' does not exist on type 'User'.
src/components/product/advanced-image-upload.tsx(86,24): error TS2345: Argument of type '{ url: string; originalName: string; fileSize: number; mimeType: string; altText: string | null; caption: string | null; }[]' is not assignable to parameter of type '{ url: string; originalName: string; fileSize: number; mimeType: string; altText?: string | undefined; caption?: string | undefined; }[]'.
  Type '{ url: string; originalName: string; fileSize: number; mimeType: string; altText: string | null; caption: string | null; }' is not assignable to type '{ url: string; originalName: string; fileSize: number; mimeType: string; altText?: string | undefined; caption?: string | undefined; }'.
    Types of property 'altText' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
src/components/product/enhanced-image-manager.tsx(115,17): error TS2345: Argument of type '{ url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; mimeType: string | null; sortOrder: number; uploadedAt: string | null; originalName: string | null; altText: string | null; }[]' is not assignable to parameter of type 'SetStateAction<ProductImage[]>'.
  Type '{ url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; mimeType: string | null; sortOrder: number; uploadedAt: string | null; originalName: string | null; altText: string | null; }[]' is not assignable to type 'ProductImage[]'.
    Type '{ url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; mimeType: string | null; sortOrder: number; uploadedAt: string | null; originalName: string | null; altText: string | null; }' is not assignable to type 'ProductImage'.
      Types of property 'originalName' are incompatible.
        Type 'string | null' is not assignable to type 'string | undefined'.
          Type 'null' is not assignable to type 'string | undefined'.
src/components/product/enhanced-image-manager.tsx(116,24): error TS2345: Argument of type '{ url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; mimeType: string | null; sortOrder: number; uploadedAt: string | null; originalName: string | null; altText: string | null; }[]' is not assignable to parameter of type 'ProductImage[]'.
  Type '{ url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; mimeType: string | null; sortOrder: number; uploadedAt: string | null; originalName: string | null; altText: string | null; }' is not assignable to type 'ProductImage'.
    Types of property 'originalName' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.
src/components/product/inventory-location-manager.tsx(95,5): error TS2353: Object literal may only specify known properties, and 'factoryId' does not exist in type '{ productId: string; }'.
src/components/product/inventory-location-manager.tsx(102,5): error TS2353: Object literal may only specify known properties, and 'productId' does not exist in type '{ status?: "ACTIVE" | "ACKNOWLEDGED" | "RESOLVED" | "DISMISSED" | undefined; limit?: number | undefined; severity?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL" | undefined; alertType?: "LOW_STOCK" | ... 7 more ... | undefined; }'.
src/components/product/inventory-location-manager.tsx(110,5): error TS2353: Object literal may only specify known properties, and 'factoryId' does not exist in type '{ productId?: string | undefined; days?: number | undefined; locationId?: string | undefined; }'.
src/components/product/inventory-location-manager.tsx(148,25): error TS2345: Argument of type '{ summary: { totalStock: number; totalReserved: number; totalAvailable: number; totalValue: number; averageCostPerUnit: number; locationCount: number; utilizationRate: number; }; productId: string; locations: { ...; }[]; productName: string; }' is not assignable to parameter of type 'any[]'.
  Type '{ summary: { totalStock: number; totalReserved: number; totalAvailable: number; totalValue: number; averageCostPerUnit: number; locationCount: number; utilizationRate: number; }; productId: string; locations: { ...; }[]; productName: string; }' is missing the following properties from type 'any[]': length, pop, push, concat, and 35 more.
src/components/product/inventory-location-manager.tsx(224,60): error TS2339: Property 'filter' does not exist on type '{ summary: { total: number; bySeverity: Record<string, number>; }; alerts: { title: string; id: string; emailSent: boolean; createdAt: string; updatedAt: string; status: AlertStatus; ... 16 more ...; notificationSentAt: string | null; }[]; }'.
src/components/product/inventory-location-manager.tsx(224,67): error TS7006: Parameter 'a' implicitly has an 'any' type.
src/components/product/inventory-location-manager.tsx(256,39): error TS2339: Property 'length' does not exist on type '{ summary: { totalStock: number; totalReserved: number; totalAvailable: number; totalValue: number; averageCostPerUnit: number; locationCount: number; utilizationRate: number; }; productId: string; locations: { ...; }[]; productName: string; }'.
src/components/product/inventory-location-manager.tsx(258,30): error TS2339: Property 'map' does not exist on type '{ summary: { totalStock: number; totalReserved: number; totalAvailable: number; totalValue: number; averageCostPerUnit: number; locationCount: number; utilizationRate: number; }; productId: string; locations: { ...; }[]; productName: string; }'.
src/components/product/inventory-location-manager.tsx(280,33): error TS2339: Property 'length' does not exist on type '{ summary: { total: number; bySeverity: Record<string, number>; }; alerts: { title: string; id: string; emailSent: boolean; createdAt: string; updatedAt: string; status: AlertStatus; ... 16 more ...; notificationSentAt: string | null; }[]; }'.
src/components/product/inventory-location-manager.tsx(282,27): error TS2339: Property 'map' does not exist on type '{ summary: { total: number; bySeverity: Record<string, number>; }; alerts: { title: string; id: string; emailSent: boolean; createdAt: string; updatedAt: string; status: AlertStatus; ... 16 more ...; notificationSentAt: string | null; }[]; }'.
src/components/product/inventory-location-manager.tsx(378,30): error TS2345: Argument of type 'string' is not assignable to parameter of type 'requiredKeys<{ locationName: string; locationCode: string | undefined; locationAddress: string | undefined; locationManager: string | undefined; stockQuantity: number; reorderPoint: number | undefined; ... 7 more ...; trackingEnabled: boolean | undefined; }> | optionalKeys<...>'.
src/components/product/inventory-location-manager.tsx(379,28): error TS2345: Argument of type 'string' is not assignable to parameter of type 'requiredKeys<{ locationName: string; locationCode: string | undefined; locationAddress: string | undefined; locationManager: string | undefined; stockQuantity: number; reorderPoint: number | undefined; ... 7 more ...; trackingEnabled: boolean | undefined; }> | optionalKeys<...>'.
src/components/product/inventory-reservation-manager.tsx(295,33): error TS2345: Argument of type '(reservation: InventoryReservation) => React.JSX.Element' is not assignable to parameter of type '(value: { id: string; createdAt: string; updatedAt: string; status: ReservationStatus; notes: string | null; createdBy: string | null; quantity: number; inventoryLocationId: string; ... 4 more ...; inventoryLocation: { ...; }; }, index: number, array: { ...; }[]) => Element'.
  Types of parameters 'reservation' and 'value' are incompatible.
    Type '{ id: string; createdAt: string; updatedAt: string; status: ReservationStatus; notes: string | null; createdBy: string | null; quantity: number; inventoryLocationId: string; ... 4 more ...; inventoryLocation: { ...; }; }' is not assignable to type 'InventoryReservation'.
      Types of property 'reservedUntil' are incompatible.
        Type 'string' is not assignable to type 'Date'.
src/components/product/professional-image-manager.tsx(135,17): error TS2345: Argument of type '{ images: { url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; ... 4 more ...; altText: string | null; }[]; totalCount: number; mainImage: { ...; }; }' is not assignable to parameter of type 'SetStateAction<ProductImage[]>'.
src/components/product/professional-image-manager.tsx(136,24): error TS2345: Argument of type '{ images: { url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; ... 4 more ...; altText: string | null; }[]; totalCount: number; mainImage: { ...; }; }' is not assignable to parameter of type 'ProductImage[]'.
  Type '{ images: { url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; ... 4 more ...; altText: string | null; }[]; totalCount: number; mainImage: { ...; }; }' is missing the following properties from type 'ProductImage[]': length, pop, push, concat, and 35 more.
src/components/product/stock-movement-manager.tsx(120,9): error TS2322: Type '{ expiryDate: string | undefined; movementType: "INBOUND"; quantity: number; reason: string; notes: string; batchNumber: string; unitCost: number; totalCost: number; currency: string; referenceId: string; referenceType: string; }' is not assignable to type '{ quantity: number; movementType: "EXPIRED" | "INBOUND" | "OUTBOUND" | "ADJUSTMENT" | "TRANSFER" | "RETURN" | "DAMAGED"; currency?: "USD" | "EUR" | "GBP" | "CNY" | "JPY" | "KRW" | "CAD" | "AUD" | undefined; ... 9 more ...; toLocationId?: string | undefined; }'.
  Types of property 'currency' are incompatible.
    Type 'string' is not assignable to type '"USD" | "EUR" | "GBP" | "CNY" | "JPY" | "KRW" | "CAD" | "AUD" | undefined'.
src/components/product/stock-movement-manager.tsx(298,30): error TS2345: Argument of type '(movement: StockMovement) => React.JSX.Element' is not assignable to parameter of type '(value: { id: string; createdAt: string; currency: Currency; notes: string | null; createdBy: string | null; quantity: number; expiryDate: string | null; inventoryLocationId: string; ... 11 more ...; newQuantity: number; }, index: number, array: { ...; }[]) => Element'.
  Types of parameters 'movement' and 'value' are incompatible.
    Type '{ id: string; createdAt: string; currency: Currency; notes: string | null; createdBy: string | null; quantity: number; expiryDate: string | null; inventoryLocationId: string; ... 11 more ...; newQuantity: number; }' is not assignable to type 'StockMovement'.
      Types of property 'reason' are incompatible.
        Type 'string | null' is not assignable to type 'string | undefined'.
          Type 'null' is not assignable to type 'string | undefined'.
src/contexts/auth-context.tsx(152,18): error TS2339: Property 'avatar' does not exist on type 'User'.
src/hooks/use-api.ts(36,31): error TS2339: Property 'getByContext' does not exist on type 'DecoratedProcedureRecord<{ createConversation: BuildProcedure<"mutation", { _config: RootConfig<{ ctx: { user: { id: string; auth0Id: string; email: string; firstName: string; lastName: string; role: "SUPER_ADMIN" | "FACTORY_OWNER" | ... 4 more ... | "CUSTOMER_ADMIN"; status: "ACTIVE" | ... 2 more ... | "PENDING_VER...'.
src/hooks/use-payload-auth.ts(209,27): error TS2339: Property 'message' does not exist on type 'never'.
src/payload/access/factories.ts(22,14): error TS2322: Type '({ req: { user } }: AccessArgs<any, any>) => boolean | { id: { equals: any; }; verificationStatus?: undefined; status?: undefined; } | { verificationStatus: { equals: string; }; status: { equals: string; }; id?: undefined; }' is not assignable to type 'Access'.
  Type 'boolean | { id: { equals: any; }; verificationStatus?: undefined; status?: undefined; } | { verificationStatus: { equals: string; }; status: { equals: string; }; id?: undefined; }' is not assignable to type 'AccessResult | Promise<AccessResult>'.
    Type '{ id: { equals: any; }; verificationStatus?: undefined; status?: undefined; }' is not assignable to type 'AccessResult | Promise<AccessResult>'.
      Type '{ id: { equals: any; }; verificationStatus?: undefined; status?: undefined; }' is not assignable to type 'Where'.
        Property 'verificationStatus' is incompatible with index signature.
          Type 'undefined' is not assignable to type 'Where[] | WhereField'.
src/payload/auth/auth0-strategy.ts(16,14): error TS2769: No overload matches this call.
  Overload 1 of 2, '(options: StrategyOptionWithRequest, verify: VerifyFunctionWithRequest): StrategyInternal', gave the following error.
    Argument of type '(accessToken: string, refreshToken: string, extraParams: any, profile: Auth0User, done: any) => Promise<any>' is not assignable to parameter of type 'VerifyFunctionWithRequest'.
      Types of parameters 'accessToken' and 'req' are incompatible.
        Type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>' is not assignable to type 'string'.
  Overload 2 of 2, '(options: StrategyOption, verify: VerifyFunction): StrategyInternal', gave the following error.
    Object literal may only specify known properties, and 'passReqToCallback' does not exist in type 'StrategyOption'.
src/payload/collections/Products.ts(12,5): error TS2322: Type '({ req: { user } }: AccessArgs<any, any>) => boolean | { factory: { equals: any; }; status?: undefined; 'factory.verificationStatus'?: undefined; } | { status: { equals: string; }; 'factory.verificationStatus': { ...; }; factory?: undefined; }' is not assignable to type 'Access'.
  Type 'boolean | { factory: { equals: any; }; status?: undefined; 'factory.verificationStatus'?: undefined; } | { status: { equals: string; }; 'factory.verificationStatus': { equals: string; }; factory?: undefined; }' is not assignable to type 'AccessResult | Promise<AccessResult>'.
    Type '{ factory: { equals: any; }; status?: undefined; 'factory.verificationStatus'?: undefined; }' is not assignable to type 'AccessResult | Promise<AccessResult>'.
      Type '{ factory: { equals: any; }; status?: undefined; 'factory.verificationStatus'?: undefined; }' is not assignable to type 'Where'.
        Property 'status' is incompatible with index signature.
          Type 'undefined' is not assignable to type 'Where[] | WhereField'.
src/payload/collections/Users.ts(22,29): error TS2339: Property 'token' does not exist on type '{ req?: PayloadRequest | undefined; token?: string | undefined; user?: unknown; } | undefined'.
src/payload/collections/Users.ts(22,36): error TS2339: Property 'user' does not exist on type '{ req?: PayloadRequest | undefined; token?: string | undefined; user?: unknown; } | undefined'.
src/payload/payload.config.ts(23,7): error TS2353: Object literal may only specify known properties, and 'favicon' does not exist in type 'MetaConfig'.
src/payload/payload.config.ts(35,5): error TS2322: Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/collections/config/types").CollectionConfig' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/collections/config/types").CollectionConfig'.
  Types of property 'access' are incompatible.
    Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined'.
      Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }'.
        Types of property 'create' are incompatible.
          Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined'.
            Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access'.
              Types of parameters 'args' and 'args' are incompatible.
                Type 'AccessArgs<any>' is not assignable to type 'AccessArgs<any, any>'.
                  Types of property 'req' are incompatible.
                    Type 'PayloadRequest' is not assignable to type 'PayloadRequest<any>'.
                      Type 'PayloadRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': get, header, accepts, acceptsCharsets, and 97 more.
src/payload/payload.config.ts(36,5): error TS2322: Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/collections/config/types").CollectionConfig' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/collections/config/types").CollectionConfig'.
  Types of property 'access' are incompatible.
    Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined'.
      Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }'.
        Types of property 'create' are incompatible.
          Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined'.
            Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access'.
              Types of parameters 'args' and 'args' are incompatible.
                Type 'AccessArgs<any>' is not assignable to type 'AccessArgs<any, any>'.
                  Types of property 'req' are incompatible.
                    Type 'PayloadRequest' is not assignable to type 'PayloadRequest<any>'.
                      Type 'PayloadRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': get, header, accepts, acceptsCharsets, and 97 more.
src/payload/payload.config.ts(37,5): error TS2322: Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/collections/config/types").CollectionConfig' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/collections/config/types").CollectionConfig'.
  Types of property 'access' are incompatible.
    Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined'.
      Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }'.
        Types of property 'create' are incompatible.
          Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined'.
            Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access'.
              Types of parameters 'args' and 'args' are incompatible.
                Type 'AccessArgs<any>' is not assignable to type 'AccessArgs<any, any>'.
                  Types of property 'req' are incompatible.
                    Type 'PayloadRequest' is not assignable to type 'PayloadRequest<any>'.
                      Type 'PayloadRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': get, header, accepts, acceptsCharsets, and 97 more.
src/payload/payload.config.ts(38,5): error TS2322: Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/collections/config/types").CollectionConfig' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/collections/config/types").CollectionConfig'.
  Types of property 'access' are incompatible.
    Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined'.
      Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }'.
        Types of property 'create' are incompatible.
          Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined'.
            Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access'.
              Types of parameters 'args' and 'args' are incompatible.
                Type 'AccessArgs<any>' is not assignable to type 'AccessArgs<any, any>'.
                  Types of property 'req' are incompatible.
                    Type 'PayloadRequest' is not assignable to type 'PayloadRequest<any>'.
                      Type 'PayloadRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': get, header, accepts, acceptsCharsets, and 97 more.
src/payload/payload.config.ts(39,5): error TS2322: Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/collections/config/types").CollectionConfig' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/collections/config/types").CollectionConfig'.
  Types of property 'access' are incompatible.
    Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined'.
      Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }'.
        Types of property 'create' are incompatible.
          Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined'.
            Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access'.
              Types of parameters 'args' and 'args' are incompatible.
                Type 'AccessArgs<any>' is not assignable to type 'AccessArgs<any, any>'.
                  Types of property 'req' are incompatible.
                    Type 'PayloadRequest' is not assignable to type 'PayloadRequest<any>'.
                      Type 'PayloadRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': get, header, accepts, acceptsCharsets, and 97 more.
src/payload/payload.config.ts(42,5): error TS2322: Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/globals/config/types").GlobalConfig' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/globals/config/types").GlobalConfig'.
  Types of property 'access' are incompatible.
    Type '{ read?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined; readDrafts?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined; readVersions?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_...' is not assignable to type '{ read?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined; readDrafts?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined; readVersions?: import("/Users/<USER>/PycharmProjects/...'.
      Type '{ read?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined; readDrafts?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined; readVersions?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_...' is not assignable to type '{ read?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined; readDrafts?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined; readVersions?: import("/Users/<USER>/PycharmProjects/...'.
        Types of property 'read' are incompatible.
          Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined'.
            Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access'.
              Types of parameters 'args' and 'args' are incompatible.
                Type 'AccessArgs<any>' is not assignable to type 'AccessArgs<any, any>'.
                  Types of property 'req' are incompatible.
                    Type 'PayloadRequest' is not assignable to type 'PayloadRequest<any>'.
                      Type 'PayloadRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': get, header, accepts, acceptsCharsets, and 97 more.
src/utils/payload-integration.ts(131,5): error TS2353: Object literal may only specify known properties, and 'adminInfo' does not exist in type 'Partial<Factory>'.
npm error Lifecycle script `type-check` failed with error:
npm error code 2
npm error path /Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web
npm error workspace web@0.1.0
npm error location /Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web
npm error command failed
npm error command sh -c tsc --noEmit
