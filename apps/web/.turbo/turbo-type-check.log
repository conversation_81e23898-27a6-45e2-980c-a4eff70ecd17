
> web@0.1.0 type-check
> tsc --noEmit

[1G[0K[96msrc/app/dashboard/recurring-orders/page.tsx[0m:[93m278[0m:[93m38[0m - [91merror[0m[90m TS2345: [0mArgument of type '(recurringOrder: RecurringOrder) => JSX.Element' is not assignable to parameter of type '(value: { description: string | null; name: string; id: string; createdAt: string; updatedAt: string; createdBy: string; template: { name: string; id: string; templateType: TemplateType; defaultCurrency: Currency; estimatedTotalAmount: number | null; }; ... 22 more ...; notifyFactory: boolean; }, index: number, arra...'.
  Types of parameters 'recurringOrder' and 'value' are incompatible.
    Type '{ description: string | null; name: string; id: string; createdAt: string; updatedAt: string; createdBy: string; template: { name: string; id: string; templateType: TemplateType; defaultCurrency: Currency; estimatedTotalAmount: number | null; }; ... 22 more ...; notifyFactory: boolean; }' is not assignable to type 'RecurringOrder'.
      Types of property 'description' are incompatible.
        Type 'string | null' is not assignable to type 'string | undefined'.
          Type 'null' is not assignable to type 'string | undefined'.

[7m278[0m                 {recurringOrders.map((recurringOrder: RecurringOrder) => (
[7m   [0m [91m                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m279[0m                   <RecurringOrderCard
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m284[0m                   />
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~[0m
[7m285[0m                 ))}
[7m   [0m [91m~~~~~~~~~~~~~~~~~[0m

[96msrc/app/dashboard/templates/[id]/edit/page.tsx[0m:[93m127[0m:[93m9[0m - [91merror[0m[90m TS2322: [0mType '{ id: string; productId: string; variantId: string | null; quantity: number; unitPrice: number | null; specifications: string | number | boolean | (string | number | boolean | (string | ... 4 more ... | null)[] | Record<...> | null)[] | Record<...> | null; notes: string | null; sortOrder: number; product: { ...; }; ...' is not assignable to type 'TemplateItem[]'.
  Type '{ id: string; productId: string; variantId: string | null; quantity: number; unitPrice: number | null; specifications: string | number | boolean | (string | number | boolean | (string | ... 4 more ... | null)[] | Record<...> | null)[] | Record<...> | null; notes: string | null; sortOrder: number; product: { ...; }; ...' is not assignable to type 'TemplateItem'.
    Types of property 'variantId' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.

[7m127[0m         items: template.items.map((item, index) => ({
[7m   [0m [91m        ~~~~~[0m

[96msrc/app/dashboard/templates/page.tsx[0m:[93m268[0m:[93m21[0m - [91merror[0m[90m TS2322: [0mType '{ description: string | null; name: string; id: string; createdAt: string; updatedAt: string; createdBy: string; factoryId: string; _count: { orders: number; recurringOrders: number; }; isActive: boolean; ... 15 more ...; estimatedTotalAmount: number | null; }' is not assignable to type 'OrderTemplate'.
  Types of property 'description' are incompatible.
    Type 'string | null' is not assignable to type 'string | undefined'.
      Type 'null' is not assignable to type 'string | undefined'.

[7m268[0m                     template={template}
[7m   [0m [91m                    ~~~~~~~~[0m

  [96msrc/app/dashboard/templates/page.tsx[0m:[93m283[0m:[93m3[0m
    [7m283[0m   template: OrderTemplate;
    [7m   [0m [96m  ~~~~~~~~[0m
    The expected type comes from property 'template' which is declared here on type 'IntrinsicAttributes & TemplateCardProps'

[96msrc/app/test/integration/page.tsx[0m:[93m129[0m:[93m17[0m - [91merror[0m[90m TS2774: [0mThis condition will always return true since this function is always defined. Did you mean to call it instead?

[7m129[0m             if (EnhancedImageManager && AdvancedImageUpload) {
[7m   [0m [91m                ~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/app/test/integration/page.tsx[0m:[93m129[0m:[93m41[0m - [91merror[0m[90m TS2774: [0mThis condition will always return true since this function is always defined. Did you mean to call it instead?

[7m129[0m             if (EnhancedImageManager && AdvancedImageUpload) {
[7m   [0m [91m                                        ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/app/test/product-edit-integration/page.tsx[0m:[93m253[0m:[93m22[0m - [91merror[0m[90m TS2774: [0mThis condition will always return true since this function is always defined. Did you mean to call it instead?

[7m253[0m             result = EnhancedImageManager ? 'Component imported successfully' : 'Component not found';
[7m   [0m [91m                     ~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/app/test/product-edit-integration/page.tsx[0m:[93m262[0m:[93m22[0m - [91merror[0m[90m TS2774: [0mThis condition will always return true since this function is always defined. Did you mean to call it instead?

[7m262[0m             result = InventoryLocationManager ? 'Component imported successfully' : 'Component not found';
[7m   [0m [91m                     ~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/app/test/product-edit-integration/page.tsx[0m:[93m271[0m:[93m22[0m - [91merror[0m[90m TS2774: [0mThis condition will always return true since this function is always defined. Did you mean to call it instead?

[7m271[0m             result = StockMovementManager ? 'Component imported successfully' : 'Component not found';
[7m   [0m [91m                     ~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/app/test/product-edit-integration/page.tsx[0m:[93m280[0m:[93m22[0m - [91merror[0m[90m TS2774: [0mThis condition will always return true since this function is always defined. Did you mean to call it instead?

[7m280[0m             result = InventoryReservationManager ? 'Component imported successfully' : 'Component not found';
[7m   [0m [91m                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/app/test/product-edit-integration/page.tsx[0m:[93m289[0m:[93m22[0m - [91merror[0m[90m TS2774: [0mThis condition will always return true since this function is always defined. Did you mean to call it instead?

[7m289[0m             result = useFormValidation ? 'Form validation hooks available' : 'Validation hooks not found';
[7m   [0m [91m                     ~~~~~~~~~~~~~~~~~[0m

[96msrc/components/auth/payload-admin-button.tsx[0m:[93m11[0m:[93m5[0m - [91merror[0m[90m TS2339: [0mProperty 'payloadToken' does not exist on type 'AuthContextType'.

[7m11[0m     payloadToken,
[7m  [0m [91m    ~~~~~~~~~~~~[0m

[96msrc/components/auth/payload-admin-button.tsx[0m:[93m30[0m:[93m22[0m - [91merror[0m[90m TS2339: [0mProperty 'factory' does not exist on type '{ firstName: string; lastName: string; role: string; avatar?: string | undefined; }'.

[7m30[0m         {payloadUser.factory && (
[7m  [0m [91m                     ~~~~~~~[0m

[96msrc/components/auth/payload-admin-button.tsx[0m:[93m70[0m:[93m24[0m - [91merror[0m[90m TS2339: [0mProperty 'payloadToken' does not exist on type 'AuthContextType'.

[7m70[0m   const { payloadUser, payloadToken, isLoading } = useAuth();
[7m  [0m [91m                       ~~~~~~~~~~~~[0m

[96msrc/components/auth/payload-admin-button.tsx[0m:[93m103[0m:[93m5[0m - [91merror[0m[90m TS2339: [0mProperty 'payloadToken' does not exist on type 'AuthContextType'.

[7m103[0m     payloadToken,
[7m   [0m [91m    ~~~~~~~~~~~~[0m

[96msrc/components/auth/payload-admin-button.tsx[0m:[93m141[0m:[93m23[0m - [91merror[0m[90m TS2339: [0mProperty 'factory' does not exist on type '{ firstName: string; lastName: string; role: string; avatar?: string | undefined; }'.

[7m141[0m         {payloadUser?.factory && (
[7m   [0m [91m                      ~~~~~~~[0m

[96msrc/components/auth/payload-admin-button.tsx[0m:[93m143[0m:[93m52[0m - [91merror[0m[90m TS2339: [0mProperty 'factory' does not exist on type '{ firstName: string; lastName: string; role: string; avatar?: string | undefined; }'.

[7m143[0m             <strong>Factory:</strong> {payloadUser.factory}
[7m   [0m [91m                                                   ~~~~~~~[0m

[96msrc/components/dashboard/dashboard-overview.tsx[0m:[93m89[0m:[93m37[0m - [91merror[0m[90m TS2339: [0mProperty 'avatar' does not exist on type 'User'.

[7m89[0m             <AvatarImage src={user?.avatar} alt={user?.firstName} />
[7m  [0m [91m                                    ~~~~~~[0m

[96msrc/components/dashboard/dashboard-overview.tsx[0m:[93m89[0m:[93m56[0m - [91merror[0m[90m TS2339: [0mProperty 'firstName' does not exist on type 'User'.

[7m89[0m             <AvatarImage src={user?.avatar} alt={user?.firstName} />
[7m  [0m [91m                                                       ~~~~~~~~~[0m

[96msrc/components/dashboard/dashboard-sidebar.tsx[0m:[93m83[0m:[93m37[0m - [91merror[0m[90m TS2339: [0mProperty 'avatar' does not exist on type 'User'.

[7m83[0m             <AvatarImage src={user?.avatar} alt={user?.firstName} />
[7m  [0m [91m                                    ~~~~~~[0m

[96msrc/components/dashboard/dashboard-sidebar.tsx[0m:[93m83[0m:[93m56[0m - [91merror[0m[90m TS2339: [0mProperty 'firstName' does not exist on type 'User'.

[7m83[0m             <AvatarImage src={user?.avatar} alt={user?.firstName} />
[7m  [0m [91m                                                       ~~~~~~~~~[0m

[96msrc/components/dashboard/message-attachment.tsx[0m:[93m259[0m:[93m20[0m - [91merror[0m[90m TS7006: [0mParameter 'prev' implicitly has an 'any' type.

[7m259[0m     setAttachments(prev => prev.filter(a => a.id !== id));
[7m   [0m [91m                   ~~~~[0m

[96msrc/components/dashboard/message-attachment.tsx[0m:[93m259[0m:[93m20[0m - [91merror[0m[90m TS2345: [0mArgument of type '(prev: any) => any' is not assignable to parameter of type 'any[]'.

[7m259[0m     setAttachments(prev => prev.filter(a => a.id !== id));
[7m   [0m [91m                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/components/dashboard/message-attachment.tsx[0m:[93m259[0m:[93m40[0m - [91merror[0m[90m TS7006: [0mParameter 'a' implicitly has an 'any' type.

[7m259[0m     setAttachments(prev => prev.filter(a => a.id !== id));
[7m   [0m [91m                                       ~[0m

[96msrc/components/dashboard/new-conversation-dialog.tsx[0m:[93m60[0m:[93m31[0m - [91merror[0m[90m TS2589: [0mType instantiation is excessively deep and possibly infinite.

[7m 60[0m   const sendMessageMutation = trpc.messages.sendMessage.useMutation({
[7m   [0m [91m                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m 61[0m     onSuccess: (_, variables) => {
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m 63[0m     },
[7m   [0m [91m~~~~~~[0m
[7m 64[0m   });
[7m   [0m [91m~~~~[0m

[96msrc/components/forms/product-form-validation.tsx[0m:[93m48[0m:[93m30[0m - [91merror[0m[90m TS2345: [0mArgument of type 'string' is not assignable to parameter of type 'requiredKeys<{ name: string; sku: string; description: string | undefined; price: number; currency: string | undefined; moq: number; status: "ACTIVE" | "INACTIVE" | "DRAFT" | "DISCONTINUED" | undefined; ... 17 more ...; keyFeatures: string | undefined; }> | optionalKeys<...>'.

[7m48[0m     isInvalid: hasFieldError(field),
[7m  [0m [91m                             ~~~~~[0m

[96msrc/components/forms/product-form-validation.tsx[0m:[93m49[0m:[93m28[0m - [91merror[0m[90m TS2345: [0mArgument of type 'string' is not assignable to parameter of type 'requiredKeys<{ name: string; sku: string; description: string | undefined; price: number; currency: string | undefined; moq: number; status: "ACTIVE" | "INACTIVE" | "DRAFT" | "DISCONTINUED" | undefined; ... 17 more ...; keyFeatures: string | undefined; }> | optionalKeys<...>'.

[7m49[0m     message: getFieldError(field),
[7m  [0m [91m                           ~~~~~[0m

[96msrc/components/onboarding/factory-onboarding-wizard.tsx[0m:[93m255[0m:[93m53[0m - [91merror[0m[90m TS2339: [0mProperty 'auth0Id' does not exist on type 'User'.

[7m255[0m       console.log('🔍 Debug - User auth0Id:', user?.auth0Id);
[7m   [0m [91m                                                    ~~~~~~~[0m

[96msrc/components/onboarding/factory-onboarding-wizard.tsx[0m:[93m260[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'auth0Id' does not exist on type 'User'.

[7m260[0m       const userId = user?.auth0Id || user?.id || user?.email;
[7m   [0m [91m                           ~~~~~~~[0m

[96msrc/components/product/advanced-image-upload.tsx[0m:[93m86[0m:[93m24[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ url: string; originalName: string; fileSize: number; mimeType: string; altText: string | null; caption: string | null; }[]' is not assignable to parameter of type '{ url: string; originalName: string; fileSize: number; mimeType: string; altText?: string | undefined; caption?: string | undefined; }[]'.
  Type '{ url: string; originalName: string; fileSize: number; mimeType: string; altText: string | null; caption: string | null; }' is not assignable to type '{ url: string; originalName: string; fileSize: number; mimeType: string; altText?: string | undefined; caption?: string | undefined; }'.
    Types of property 'altText' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.

[7m86[0m       onImagesUploaded(uploadedImages);
[7m  [0m [91m                       ~~~~~~~~~~~~~~[0m

[96msrc/components/product/enhanced-image-manager.tsx[0m:[93m115[0m:[93m17[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; mimeType: string | null; sortOrder: number; uploadedAt: string | null; originalName: string | null; altText: string | null; }[]' is not assignable to parameter of type 'SetStateAction<ProductImage[]>'.
  Type '{ url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; mimeType: string | null; sortOrder: number; uploadedAt: string | null; originalName: string | null; altText: string | null; }[]' is not assignable to type 'ProductImage[]'.
    Type '{ url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; mimeType: string | null; sortOrder: number; uploadedAt: string | null; originalName: string | null; altText: string | null; }' is not assignable to type 'ProductImage'.
      Types of property 'originalName' are incompatible.
        Type 'string | null' is not assignable to type 'string | undefined'.
          Type 'null' is not assignable to type 'string | undefined'.

[7m115[0m       setImages(productImages.images);
[7m   [0m [91m                ~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/components/product/enhanced-image-manager.tsx[0m:[93m116[0m:[93m24[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; mimeType: string | null; sortOrder: number; uploadedAt: string | null; originalName: string | null; altText: string | null; }[]' is not assignable to parameter of type 'ProductImage[]'.
  Type '{ url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; mimeType: string | null; sortOrder: number; uploadedAt: string | null; originalName: string | null; altText: string | null; }' is not assignable to type 'ProductImage'.
    Types of property 'originalName' are incompatible.
      Type 'string | null' is not assignable to type 'string | undefined'.
        Type 'null' is not assignable to type 'string | undefined'.

[7m116[0m       onImagesChange?.(productImages.images);
[7m   [0m [91m                       ~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/components/product/inventory-location-manager.tsx[0m:[93m95[0m:[93m5[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'factoryId' does not exist in type '{ productId: string; }'.

[7m95[0m     factoryId
[7m  [0m [91m    ~~~~~~~~~[0m

[96msrc/components/product/inventory-location-manager.tsx[0m:[93m102[0m:[93m5[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'productId' does not exist in type '{ status?: "ACTIVE" | "ACKNOWLEDGED" | "RESOLVED" | "DISMISSED" | undefined; limit?: number | undefined; severity?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL" | undefined; alertType?: "LOW_STOCK" | ... 7 more ... | undefined; }'.

[7m102[0m     productId,
[7m   [0m [91m    ~~~~~~~~~[0m

[96msrc/components/product/inventory-location-manager.tsx[0m:[93m110[0m:[93m5[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'factoryId' does not exist in type '{ productId?: string | undefined; days?: number | undefined; locationId?: string | undefined; }'.

[7m110[0m     factoryId
[7m   [0m [91m    ~~~~~~~~~[0m

[96msrc/components/product/inventory-location-manager.tsx[0m:[93m148[0m:[93m25[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ summary: { totalStock: number; totalReserved: number; totalAvailable: number; totalValue: number; averageCostPerUnit: number; locationCount: number; utilizationRate: number; }; productId: string; locations: { ...; }[]; productName: string; }' is not assignable to parameter of type 'any[]'.
  Type '{ summary: { totalStock: number; totalReserved: number; totalAvailable: number; totalValue: number; averageCostPerUnit: number; locationCount: number; utilizationRate: number; }; productId: string; locations: { ...; }[]; productName: string; }' is missing the following properties from type 'any[]': length, pop, push, concat, and 35 more.

[7m148[0m       onLocationsChange(locations);
[7m   [0m [91m                        ~~~~~~~~~[0m

[96msrc/components/product/inventory-location-manager.tsx[0m:[93m224[0m:[93m60[0m - [91merror[0m[90m TS2339: [0mProperty 'filter' does not exist on type '{ summary: { total: number; bySeverity: Record<string, number>; }; alerts: { title: string; id: string; emailSent: boolean; createdAt: string; updatedAt: string; status: AlertStatus; ... 16 more ...; notificationSentAt: string | null; }[]; }'.

[7m224[0m               <TabsTrigger value="alerts">Alerts ({alerts?.filter(a => !a.isAcknowledged).length || 0})</TabsTrigger>
[7m   [0m [91m                                                           ~~~~~~[0m

[96msrc/components/product/inventory-location-manager.tsx[0m:[93m224[0m:[93m67[0m - [91merror[0m[90m TS7006: [0mParameter 'a' implicitly has an 'any' type.

[7m224[0m               <TabsTrigger value="alerts">Alerts ({alerts?.filter(a => !a.isAcknowledged).length || 0})</TabsTrigger>
[7m   [0m [91m                                                                  ~[0m

[96msrc/components/product/inventory-location-manager.tsx[0m:[93m256[0m:[93m39[0m - [91merror[0m[90m TS2339: [0mProperty 'length' does not exist on type '{ summary: { totalStock: number; totalReserved: number; totalAvailable: number; totalValue: number; averageCostPerUnit: number; locationCount: number; utilizationRate: number; }; productId: string; locations: { ...; }[]; productName: string; }'.

[7m256[0m               {locations && locations.length > 0 ? (
[7m   [0m [91m                                      ~~~~~~[0m

[96msrc/components/product/inventory-location-manager.tsx[0m:[93m258[0m:[93m30[0m - [91merror[0m[90m TS2339: [0mProperty 'map' does not exist on type '{ summary: { totalStock: number; totalReserved: number; totalAvailable: number; totalValue: number; averageCostPerUnit: number; locationCount: number; utilizationRate: number; }; productId: string; locations: { ...; }[]; productName: string; }'.

[7m258[0m                   {locations.map((location: InventoryLocation) => (
[7m   [0m [91m                             ~~~[0m

[96msrc/components/product/inventory-location-manager.tsx[0m:[93m280[0m:[93m33[0m - [91merror[0m[90m TS2339: [0mProperty 'length' does not exist on type '{ summary: { total: number; bySeverity: Record<string, number>; }; alerts: { title: string; id: string; emailSent: boolean; createdAt: string; updatedAt: string; status: AlertStatus; ... 16 more ...; notificationSentAt: string | null; }[]; }'.

[7m280[0m               {alerts && alerts.length > 0 ? (
[7m   [0m [91m                                ~~~~~~[0m

[96msrc/components/product/inventory-location-manager.tsx[0m:[93m282[0m:[93m27[0m - [91merror[0m[90m TS2339: [0mProperty 'map' does not exist on type '{ summary: { total: number; bySeverity: Record<string, number>; }; alerts: { title: string; id: string; emailSent: boolean; createdAt: string; updatedAt: string; status: AlertStatus; ... 16 more ...; notificationSentAt: string | null; }[]; }'.

[7m282[0m                   {alerts.map((alert: InventoryAlert) => (
[7m   [0m [91m                          ~~~[0m

[96msrc/components/product/inventory-location-manager.tsx[0m:[93m378[0m:[93m30[0m - [91merror[0m[90m TS2345: [0mArgument of type 'string' is not assignable to parameter of type 'requiredKeys<{ locationName: string; locationCode: string | undefined; locationAddress: string | undefined; locationManager: string | undefined; stockQuantity: number; reorderPoint: number | undefined; ... 7 more ...; trackingEnabled: boolean | undefined; }> | optionalKeys<...>'.

[7m378[0m     isInvalid: hasFieldError(field),
[7m   [0m [91m                             ~~~~~[0m

[96msrc/components/product/inventory-location-manager.tsx[0m:[93m379[0m:[93m28[0m - [91merror[0m[90m TS2345: [0mArgument of type 'string' is not assignable to parameter of type 'requiredKeys<{ locationName: string; locationCode: string | undefined; locationAddress: string | undefined; locationManager: string | undefined; stockQuantity: number; reorderPoint: number | undefined; ... 7 more ...; trackingEnabled: boolean | undefined; }> | optionalKeys<...>'.

[7m379[0m     message: getFieldError(field),
[7m   [0m [91m                           ~~~~~[0m

[96msrc/components/product/inventory-reservation-manager.tsx[0m:[93m295[0m:[93m33[0m - [91merror[0m[90m TS2345: [0mArgument of type '(reservation: InventoryReservation) => React.JSX.Element' is not assignable to parameter of type '(value: { id: string; createdAt: string; updatedAt: string; status: ReservationStatus; notes: string | null; createdBy: string | null; quantity: number; inventoryLocationId: string; ... 4 more ...; inventoryLocation: { ...; }; }, index: number, array: { ...; }[]) => Element'.
  Types of parameters 'reservation' and 'value' are incompatible.
    Type '{ id: string; createdAt: string; updatedAt: string; status: ReservationStatus; notes: string | null; createdBy: string | null; quantity: number; inventoryLocationId: string; ... 4 more ...; inventoryLocation: { ...; }; }' is not assignable to type 'InventoryReservation'.
      Types of property 'reservedUntil' are incompatible.
        Type 'string' is not assignable to type 'Date'.

[7m295[0m               {reservations.map((reservation: InventoryReservation) => {
[7m   [0m [91m                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/components/product/professional-image-manager.tsx[0m:[93m135[0m:[93m17[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ images: { url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; ... 4 more ...; altText: string | null; }[]; totalCount: number; mainImage: { ...; }; }' is not assignable to parameter of type 'SetStateAction<ProductImage[]>'.

[7m135[0m       setImages(productImages);
[7m   [0m [91m                ~~~~~~~~~~~~~[0m

[96msrc/components/product/professional-image-manager.tsx[0m:[93m136[0m:[93m24[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ images: { url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; ... 4 more ...; altText: string | null; }[]; totalCount: number; mainImage: { ...; }; }' is not assignable to parameter of type 'ProductImage[]'.
  Type '{ images: { url: string; fileSize: number | null; id: string; createdAt: string; updatedAt: string; status: ImageStatus; caption: string | null; alt: string | null; isMain: boolean; ... 4 more ...; altText: string | null; }[]; totalCount: number; mainImage: { ...; }; }' is missing the following properties from type 'ProductImage[]': length, pop, push, concat, and 35 more.

[7m136[0m       onImagesChange?.(productImages);
[7m   [0m [91m                       ~~~~~~~~~~~~~[0m

[96msrc/components/product/stock-movement-manager.tsx[0m:[93m120[0m:[93m9[0m - [91merror[0m[90m TS2322: [0mType '{ expiryDate: string | undefined; movementType: "INBOUND"; quantity: number; reason: string; notes: string; batchNumber: string; unitCost: number; totalCost: number; currency: string; referenceId: string; referenceType: string; }' is not assignable to type '{ quantity: number; movementType: "EXPIRED" | "INBOUND" | "OUTBOUND" | "ADJUSTMENT" | "TRANSFER" | "RETURN" | "DAMAGED"; currency?: "USD" | "EUR" | "GBP" | "CNY" | "JPY" | "KRW" | "CAD" | "AUD" | undefined; ... 9 more ...; toLocationId?: string | undefined; }'.
  Types of property 'currency' are incompatible.
    Type 'string' is not assignable to type '"USD" | "EUR" | "GBP" | "CNY" | "JPY" | "KRW" | "CAD" | "AUD" | undefined'.

[7m120[0m         movementData: {
[7m   [0m [91m        ~~~~~~~~~~~~[0m

  [96m../api/src/routers/products.ts[0m:[93m2450[0m:[93m7[0m
    [7m2450[0m       movementData: z.object({
    [7m    [0m [96m      ~~~~~~~~~~~~~~~~~~~~~~~~[0m
    [7m2451[0m         movementType: z.enum(['INBOUND', 'OUTBOUND', 'ADJUSTMENT', 'TRANSFER', 'RETURN', 'DAMAGED', 'EXPIRED']),
    [7m    [0m [96m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
    [7m ...[0m 
    [7m2463[0m         toLocationId: z.string().cuid().optional(),
    [7m    [0m [96m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
    [7m2464[0m       }),
    [7m    [0m [96m~~~~~~~~[0m
    The expected type comes from property 'movementData' which is declared here on type '{ inventoryLocationId: string; movementData: { quantity: number; movementType: "EXPIRED" | "INBOUND" | "OUTBOUND" | "ADJUSTMENT" | "TRANSFER" | "RETURN" | "DAMAGED"; currency?: "USD" | ... 7 more ... | undefined; ... 9 more ...; toLocationId?: string | undefined; }; }'

[96msrc/components/product/stock-movement-manager.tsx[0m:[93m298[0m:[93m30[0m - [91merror[0m[90m TS2345: [0mArgument of type '(movement: StockMovement) => React.JSX.Element' is not assignable to parameter of type '(value: { id: string; createdAt: string; currency: Currency; notes: string | null; createdBy: string | null; quantity: number; expiryDate: string | null; inventoryLocationId: string; ... 11 more ...; newQuantity: number; }, index: number, array: { ...; }[]) => Element'.
  Types of parameters 'movement' and 'value' are incompatible.
    Type '{ id: string; createdAt: string; currency: Currency; notes: string | null; createdBy: string | null; quantity: number; expiryDate: string | null; inventoryLocationId: string; ... 11 more ...; newQuantity: number; }' is not assignable to type 'StockMovement'.
      Types of property 'reason' are incompatible.
        Type 'string | null' is not assignable to type 'string | undefined'.
          Type 'null' is not assignable to type 'string | undefined'.

[7m298[0m               {movements.map((movement: StockMovement) => {
[7m   [0m [91m                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/contexts/auth-context.tsx[0m:[93m152[0m:[93m18[0m - [91merror[0m[90m TS2339: [0mProperty 'avatar' does not exist on type 'User'.

[7m152[0m     avatar: user.avatar,
[7m   [0m [91m                 ~~~~~~[0m

[96msrc/hooks/use-api.ts[0m:[93m36[0m:[93m31[0m - [91merror[0m[90m TS2339: [0mProperty 'getByContext' does not exist on type 'DecoratedProcedureRecord<{ createConversation: BuildProcedure<"mutation", { _config: RootConfig<{ ctx: { user: { id: string; auth0Id: string; email: string; firstName: string; lastName: string; role: "SUPER_ADMIN" | "FACTORY_OWNER" | ... 4 more ... | "CUSTOMER_ADMIN"; status: "ACTIVE" | ... 2 more ... | "PENDING_VER...'.

[7m36[0m   getByContext: trpc.messages.getByContext.useQuery,
[7m  [0m [91m                              ~~~~~~~~~~~~[0m

[96msrc/hooks/use-payload-auth.ts[0m:[93m209[0m:[93m27[0m - [91merror[0m[90m TS2339: [0mProperty 'message' does not exist on type 'never'.

[7m209[0m         error: auth0Error.message,
[7m   [0m [91m                          ~~~~~~~[0m

[96msrc/payload/access/factories.ts[0m:[93m22[0m:[93m14[0m - [91merror[0m[90m TS2322: [0mType '({ req: { user } }: AccessArgs<any, any>) => boolean | { id: { equals: any; }; verificationStatus?: undefined; status?: undefined; } | { verificationStatus: { equals: string; }; status: { equals: string; }; id?: undefined; }' is not assignable to type 'Access'.
  Type 'boolean | { id: { equals: any; }; verificationStatus?: undefined; status?: undefined; } | { verificationStatus: { equals: string; }; status: { equals: string; }; id?: undefined; }' is not assignable to type 'AccessResult | Promise<AccessResult>'.
    Type '{ id: { equals: any; }; verificationStatus?: undefined; status?: undefined; }' is not assignable to type 'AccessResult | Promise<AccessResult>'.
      Type '{ id: { equals: any; }; verificationStatus?: undefined; status?: undefined; }' is not assignable to type 'Where'.
        Property 'verificationStatus' is incompatible with index signature.
          Type 'undefined' is not assignable to type 'Where[] | WhereField'.

[7m22[0m export const canReadFactory: Access = ({ req: { user } }) => {
[7m  [0m [91m             ~~~~~~~~~~~~~~[0m

[96msrc/payload/auth/auth0-strategy.ts[0m:[93m16[0m:[93m14[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.
  Overload 1 of 2, '(options: StrategyOptionWithRequest, verify: VerifyFunctionWithRequest): StrategyInternal', gave the following error.
    Argument of type '(accessToken: string, refreshToken: string, extraParams: any, profile: Auth0User, done: any) => Promise<any>' is not assignable to parameter of type 'VerifyFunctionWithRequest'.
      Types of parameters 'accessToken' and 'req' are incompatible.
        Type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>' is not assignable to type 'string'.
  Overload 2 of 2, '(options: StrategyOption, verify: VerifyFunction): StrategyInternal', gave the following error.
    Object literal may only specify known properties, and 'passReqToCallback' does not exist in type 'StrategyOption'.

[7m16[0m   return new Auth0Strategy(
[7m  [0m [91m             ~~~~~~~~~~~~~[0m


[96msrc/payload/collections/Products.ts[0m:[93m12[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType '({ req: { user } }: AccessArgs<any, any>) => boolean | { factory: { equals: any; }; status?: undefined; 'factory.verificationStatus'?: undefined; } | { status: { equals: string; }; 'factory.verificationStatus': { ...; }; factory?: undefined; }' is not assignable to type 'Access'.
  Type 'boolean | { factory: { equals: any; }; status?: undefined; 'factory.verificationStatus'?: undefined; } | { status: { equals: string; }; 'factory.verificationStatus': { equals: string; }; factory?: undefined; }' is not assignable to type 'AccessResult | Promise<AccessResult>'.
    Type '{ factory: { equals: any; }; status?: undefined; 'factory.verificationStatus'?: undefined; }' is not assignable to type 'AccessResult | Promise<AccessResult>'.
      Type '{ factory: { equals: any; }; status?: undefined; 'factory.verificationStatus'?: undefined; }' is not assignable to type 'Where'.
        Property 'status' is incompatible with index signature.
          Type 'undefined' is not assignable to type 'Where[] | WhereField'.

[7m12[0m     read: ({ req: { user } }) => {
[7m  [0m [91m    ~~~~[0m

[96msrc/payload/collections/Users.ts[0m:[93m22[0m:[93m29[0m - [91merror[0m[90m TS2339: [0mProperty 'token' does not exist on type '{ req?: PayloadRequest | undefined; token?: string | undefined; user?: unknown; } | undefined'.

[7m22[0m       generateEmailHTML: ({ token, user }) => {
[7m  [0m [91m                            ~~~~~[0m

[96msrc/payload/collections/Users.ts[0m:[93m22[0m:[93m36[0m - [91merror[0m[90m TS2339: [0mProperty 'user' does not exist on type '{ req?: PayloadRequest | undefined; token?: string | undefined; user?: unknown; } | undefined'.

[7m22[0m       generateEmailHTML: ({ token, user }) => {
[7m  [0m [91m                                   ~~~~[0m

[96msrc/payload/payload.config.ts[0m:[93m23[0m:[93m7[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'favicon' does not exist in type 'MetaConfig'.

[7m23[0m       favicon: '/favicon.ico',
[7m  [0m [91m      ~~~~~~~[0m

[96msrc/payload/payload.config.ts[0m:[93m35[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/collections/config/types").CollectionConfig' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/collections/config/types").CollectionConfig'.
  Types of property 'access' are incompatible.
    Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined'.
      Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }'.
        Types of property 'create' are incompatible.
          Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined'.
            Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access'.
              Types of parameters 'args' and 'args' are incompatible.
                Type 'AccessArgs<any>' is not assignable to type 'AccessArgs<any, any>'.
                  Types of property 'req' are incompatible.
                    Type 'PayloadRequest' is not assignable to type 'PayloadRequest<any>'.
                      Type 'PayloadRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': get, header, accepts, acceptsCharsets, and 97 more.

[7m35[0m     Users,
[7m  [0m [91m    ~~~~~[0m

[96msrc/payload/payload.config.ts[0m:[93m36[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/collections/config/types").CollectionConfig' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/collections/config/types").CollectionConfig'.
  Types of property 'access' are incompatible.
    Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined'.
      Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }'.
        Types of property 'create' are incompatible.
          Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined'.
            Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access'.
              Types of parameters 'args' and 'args' are incompatible.
                Type 'AccessArgs<any>' is not assignable to type 'AccessArgs<any, any>'.
                  Types of property 'req' are incompatible.
                    Type 'PayloadRequest' is not assignable to type 'PayloadRequest<any>'.
                      Type 'PayloadRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': get, header, accepts, acceptsCharsets, and 97 more.

[7m36[0m     Factories,
[7m  [0m [91m    ~~~~~~~~~[0m

[96msrc/payload/payload.config.ts[0m:[93m37[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/collections/config/types").CollectionConfig' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/collections/config/types").CollectionConfig'.
  Types of property 'access' are incompatible.
    Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined'.
      Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }'.
        Types of property 'create' are incompatible.
          Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined'.
            Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access'.
              Types of parameters 'args' and 'args' are incompatible.
                Type 'AccessArgs<any>' is not assignable to type 'AccessArgs<any, any>'.
                  Types of property 'req' are incompatible.
                    Type 'PayloadRequest' is not assignable to type 'PayloadRequest<any>'.
                      Type 'PayloadRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': get, header, accepts, acceptsCharsets, and 97 more.

[7m37[0m     Products,
[7m  [0m [91m    ~~~~~~~~[0m

[96msrc/payload/payload.config.ts[0m:[93m38[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/collections/config/types").CollectionConfig' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/collections/config/types").CollectionConfig'.
  Types of property 'access' are incompatible.
    Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined'.
      Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }'.
        Types of property 'create' are incompatible.
          Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined'.
            Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access'.
              Types of parameters 'args' and 'args' are incompatible.
                Type 'AccessArgs<any>' is not assignable to type 'AccessArgs<any, any>'.
                  Types of property 'req' are incompatible.
                    Type 'PayloadRequest' is not assignable to type 'PayloadRequest<any>'.
                      Type 'PayloadRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': get, header, accepts, acceptsCharsets, and 97 more.

[7m38[0m     Categories,
[7m  [0m [91m    ~~~~~~~~~~[0m

[96msrc/payload/payload.config.ts[0m:[93m39[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/collections/config/types").CollectionConfig' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/collections/config/types").CollectionConfig'.
  Types of property 'access' are incompatible.
    Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; } | undefined'.
      Type '{ admin?: ((args?: any) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }' is not assignable to type '{ admin?: (({ req }: { req: PayloadRequest; }) => boolean | Promise<boolean>) | undefined; create?: Access | undefined; delete?: Access | undefined; read?: Access | undefined; readVersions?: Access | undefined; unlock?: Access | undefined; update?: Access | undefined; }'.
        Types of property 'create' are incompatible.
          Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined'.
            Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access'.
              Types of parameters 'args' and 'args' are incompatible.
                Type 'AccessArgs<any>' is not assignable to type 'AccessArgs<any, any>'.
                  Types of property 'req' are incompatible.
                    Type 'PayloadRequest' is not assignable to type 'PayloadRequest<any>'.
                      Type 'PayloadRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': get, header, accepts, acceptsCharsets, and 97 more.

[7m39[0m     Media,
[7m  [0m [91m    ~~~~~[0m

[96msrc/payload/payload.config.ts[0m:[93m42[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/globals/config/types").GlobalConfig' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/globals/config/types").GlobalConfig'.
  Types of property 'access' are incompatible.
    Type '{ read?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined; readDrafts?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined; readVersions?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_...' is not assignable to type '{ read?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined; readDrafts?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined; readVersions?: import("/Users/<USER>/PycharmProjects/...'.
      Type '{ read?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined; readDrafts?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined; readVersions?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_...' is not assignable to type '{ read?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined; readDrafts?: import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined; readVersions?: import("/Users/<USER>/PycharmProjects/...'.
        Types of property 'read' are incompatible.
          Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access | undefined' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access | undefined'.
            Type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/node_modules/payload/dist/config/types").Access' is not assignable to type 'import("/Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web/node_modules/payload/dist/config/types").Access'.
              Types of parameters 'args' and 'args' are incompatible.
                Type 'AccessArgs<any>' is not assignable to type 'AccessArgs<any, any>'.
                  Types of property 'req' are incompatible.
                    Type 'PayloadRequest' is not assignable to type 'PayloadRequest<any>'.
                      Type 'PayloadRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': get, header, accepts, acceptsCharsets, and 97 more.

[7m42[0m     FactorySettings,
[7m  [0m [91m    ~~~~~~~~~~~~~~~[0m

[96msrc/utils/payload-integration.ts[0m:[93m131[0m:[93m5[0m - [91merror[0m[90m TS2353: [0mObject literal may only specify known properties, and 'adminInfo' does not exist in type 'Partial<Factory>'.

[7m131[0m     adminInfo: {
[7m   [0m [91m    ~~~~~~~~~[0m


Found 64 errors in 27 files.

Errors  Files
     1  src/app/dashboard/recurring-orders/page.tsx[90m:278[0m
     1  src/app/dashboard/templates/[id]/edit/page.tsx[90m:127[0m
     1  src/app/dashboard/templates/page.tsx[90m:268[0m
     2  src/app/test/integration/page.tsx[90m:129[0m
     5  src/app/test/product-edit-integration/page.tsx[90m:253[0m
     6  src/components/auth/payload-admin-button.tsx[90m:11[0m
     2  src/components/dashboard/dashboard-overview.tsx[90m:89[0m
     2  src/components/dashboard/dashboard-sidebar.tsx[90m:83[0m
     3  src/components/dashboard/message-attachment.tsx[90m:259[0m
     1  src/components/dashboard/new-conversation-dialog.tsx[90m:60[0m
     2  src/components/forms/product-form-validation.tsx[90m:48[0m
     2  src/components/onboarding/factory-onboarding-wizard.tsx[90m:255[0m
     1  src/components/product/advanced-image-upload.tsx[90m:86[0m
     2  src/components/product/enhanced-image-manager.tsx[90m:115[0m
    12  src/components/product/inventory-location-manager.tsx[90m:95[0m
     1  src/components/product/inventory-reservation-manager.tsx[90m:295[0m
     2  src/components/product/professional-image-manager.tsx[90m:135[0m
     2  src/components/product/stock-movement-manager.tsx[90m:120[0m
     1  src/contexts/auth-context.tsx[90m:152[0m
     1  src/hooks/use-api.ts[90m:36[0m
     1  src/hooks/use-payload-auth.ts[90m:209[0m
     1  src/payload/access/factories.ts[90m:22[0m
     1  src/payload/auth/auth0-strategy.ts[90m:16[0m
     1  src/payload/collections/Products.ts[90m:12[0m
     2  src/payload/collections/Users.ts[90m:22[0m
     7  src/payload/payload.config.ts[90m:23[0m
     1  src/utils/payload-integration.ts[90m:131[0m
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m Lifecycle script `type-check` failed with error:
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mcode[39m [33m1[39m
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mpath[39m /Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mworkspace[39m web@0.1.0
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mlocation[39m /Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/web
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m command failed
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mcommand[39m sh -c tsc --noEmit
[1G[0K⠙[1G[0K
